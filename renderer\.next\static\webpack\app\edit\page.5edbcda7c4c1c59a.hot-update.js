"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/NoteEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/NoteEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoteEditor: () => (/* binding */ NoteEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_TagInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/TagInput */ \"(app-pages-browser)/./src/components/ui/TagInput.tsx\");\n/* harmony import */ var _CherryMarkdownEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CherryMarkdownEditor */ \"(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\");\n/* harmony import */ var _uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/react-markdown-preview */ \"(app-pages-browser)/./node_modules/@uiw/react-markdown-preview/esm/index.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./src/lib/ai-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _contexts_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/i18n */ \"(app-pages-browser)/./src/contexts/i18n.tsx\");\n/* __next_internal_client_entry_do_not_use__ NoteEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NoteEditor(param) {\n    let { note, onSave, onDelete } = param;\n    _s();\n    const { t } = (0,_contexts_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18n)();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const [aiLoading, setAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiResult, setAiResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResultDialogVisible, setIsResultDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpandDialogVisible, setIsExpandDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandPrompt, setExpandPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [suggestedTitle, setSuggestedTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTitleAlertVisible, setIsTitleAlertVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 選取文字處理將由 CherryMarkdownEditor 元件處理\n    const handleSelectionChange = (selectedText)=>{\n        setSelectedText(selectedText);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoteEditor.useEffect\": ()=>{\n            if (note) {\n                setTitle(note.title);\n                setContent(note.content);\n                setTags(Array.isArray(note.tags) ? note.tags : []);\n                setIsEditing(false);\n            } else {\n                setTitle(\"\");\n                setContent(\"\");\n                setTags([]);\n                setIsEditing(true);\n            }\n        }\n    }[\"NoteEditor.useEffect\"], [\n        note\n    ]);\n    const handleSave = ()=>{\n        onSave((note === null || note === void 0 ? void 0 : note.id) || null, title, content, tags);\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (note) {\n            onDelete(note.id);\n        }\n    };\n    const handleAiAction = async (action, payload)=>{\n        setAiLoading(action);\n        try {\n            let result;\n            switch(action){\n                case \"summarize\":\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting summarize streaming\");\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.summarizeStream)(content, (chunk)=>{\n                        console.log(\"Received summarize chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Summarize streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"generateTitle\":\n                    let titleResult = \"\";\n                    console.log(\"Starting title generation streaming\");\n                    await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.generateTitleStream)(content, (chunk)=>{\n                        console.log(\"Received title chunk:\", chunk);\n                        titleResult += chunk;\n                    }).catch((error)=>{\n                        console.error(\"Title streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                        throw error;\n                    });\n                    setSuggestedTitle(titleResult.trim());\n                    setIsTitleAlertVisible(true);\n                    break;\n                case \"polish\":\n                    if (!selectedText) {\n                        toast({\n                            title: t(\"editor.selection_error\"),\n                            description: t(\"editor.select_text_first\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting polish streaming\");\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.polishStream)(selectedText, (chunk)=>{\n                        console.log(\"Received polish chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Polish streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"expand\":\n                    if (!payload || !payload.trim()) {\n                        toast({\n                            title: t(\"editor.input_error\"),\n                            description: t(\"editor.enter_valid_prompt\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsExpandDialogVisible(false);\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting expand streaming with payload:\", payload);\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.expandContentStream)(payload, (chunk)=>{\n                        console.log(\"Received expand chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Expand streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"changeTone\":\n                    if (!selectedText) {\n                        toast({\n                            title: t(\"editor.selection_error\"),\n                            description: t(\"editor.select_tone_text_first\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting changeTone streaming with tone:\", payload);\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.changeToneStream)(selectedText, payload, (chunk)=>{\n                        console.log(\"Received changeTone chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"ChangeTone streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"generateTags\":\n                    if (!content.trim()) {\n                        toast({\n                            title: t(\"editor.input_error\"),\n                            description: t(\"editor.content_required_for_tags\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    try {\n                        console.log(\"Starting tag generation\");\n                        const newTags = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.generateTags)(content);\n                        const uniqueTags = [\n                            ...new Set([\n                                ...tags,\n                                ...newTags\n                            ])\n                        ];\n                        setTags(uniqueTags);\n                        toast({\n                            title: t(\"editor.tags_generated\"),\n                            description: t(\"editor.tags_added_successfully\"),\n                            variant: \"default\"\n                        });\n                    } catch (error) {\n                        console.error(\"Tag generation error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    }\n                    break;\n            }\n        } catch (error) {\n            const message = error instanceof Error ? error.message : t(\"editor.unknown_error\");\n            toast({\n                title: t(\"editor.ai_operation_failed\"),\n                description: message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setAiLoading(false);\n        }\n    };\n    const handleInsertResult = ()=>{\n        setContent(\"\".concat(content, \"\\n\\n---\\n\\n\").concat(aiResult));\n        setIsResultDialogVisible(false);\n    };\n    const handleReplaceResult = ()=>{\n        setContent(content.replace(selectedText, aiResult));\n        setIsResultDialogVisible(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: title,\n                                            onChange: (e)=>setTitle(e.target.value),\n                                            placeholder: t(\"editor.title_placeholder\"),\n                                            className: \"h-auto max-w-2xl px-0 py-1 text-xl font-semibold bg-transparent border-none shadow-none focus-visible:ring-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            disabled: !!aiLoading,\n                                                            children: [\n                                                                aiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                t(\"editor.ai_tools\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"summarize\"),\n                                                                children: t(\"editor.summarize\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"generateTitle\"),\n                                                                children: t(\"editor.generate_title\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"polish\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.polish_selected\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>setIsExpandDialogVisible(true),\n                                                                children: t(\"editor.expand_content\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"changeTone\", \"專業\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.change_tone_professional\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"changeTone\", \"休閒\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.change_tone_casual\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"generateTags\"),\n                                                                disabled: !content.trim() || !!aiLoading,\n                                                                children: t(\"editor.generate_tags\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleSave,\n                                                size: \"sm\",\n                                                variant: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.save\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>setIsEditing(true),\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.edit\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleDelete,\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.delete\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TagInput__WEBPACK_IMPORTED_MODULE_5__.TagInput, {\n                                    value: tags,\n                                    onChange: setTags,\n                                    placeholder: t(\"editor.tags_placeholder\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: Array.isArray(tags) && tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-sm rounded-full bg-secondary text-secondary-foreground\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col flex-1 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CherryMarkdownEditor__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ref: editorRef,\n                            value: content,\n                            onChange: (value)=>setContent(value || \"\"),\n                            preview: isEditing ? \"edit\" : \"preview\",\n                            hideToolbar: !isEditing,\n                            className: \"flex-1\",\n                            onSelectionChange: handleSelectionChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isResultDialogVisible,\n                onOpenChange: setIsResultDialogVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: t(\"editor.ai_result_title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 my-4 border rounded-md bg-muted max-h-[70vh] overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                source: aiResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsResultDialogVisible(false),\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleInsertResult,\n                                    variant: \"secondary\",\n                                    children: t(\"editor.insert_at_end\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleReplaceResult,\n                                    variant: \"secondary\",\n                                    children: t(\"editor.replace_selected\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isExpandDialogVisible,\n                onOpenChange: setIsExpandDialogVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: t(\"editor.expand_content_title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                            placeholder: t(\"editor.expand_placeholder\"),\n                            value: expandPrompt,\n                            onChange: (e)=>setExpandPrompt(e.target.value),\n                            disabled: aiLoading === \"expand\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsExpandDialogVisible(false),\n                                    disabled: aiLoading === \"expand\",\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleAiAction(\"expand\", expandPrompt),\n                                    variant: \"secondary\",\n                                    disabled: !expandPrompt.trim() || aiLoading === \"expand\",\n                                    children: aiLoading === \"expand\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            t(\"editor.generating\")\n                                        ]\n                                    }, void 0, true) : t(\"editor.generate\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialog, {\n                open: isTitleAlertVisible,\n                onOpenChange: setIsTitleAlertVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogTitle, {\n                                    children: t(\"editor.suggested_title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogDescription, {\n                                    children: t(\"editor.suggested_title_desc\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 font-semibold border rounded-md bg-muted\",\n                            children: suggestedTitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogCancel, {\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogAction, {\n                                    onClick: ()=>{\n                                        setTitle(suggestedTitle);\n                                        setIsTitleAlertVisible(false);\n                                    },\n                                    className: \"text-white bg-black hover:bg-gray-800\",\n                                    children: t(\"editor.replace\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 505,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NoteEditor, \"ToC0ksbA5+BVBrsULPCDoVCYPX8=\", false, function() {\n    return [\n        _contexts_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18n,\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast\n    ];\n});\n_c = NoteEditor;\nvar _c;\n$RefreshReg$(_c, \"NoteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL05vdGVFZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVvRDtBQUNiO0FBQ1M7QUFDRjtBQUNNO0FBQ21DO0FBQzdCO0FBT25CO0FBT1A7QUFVTTtBQUNjO0FBQ1A7QUFhbkI7QUFDMkM7QUFDM0I7QUE2Qm5DLFNBQVN5QyxXQUFXLEtBQTJDO1FBQTNDLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQW1CLEdBQTNDOztJQUN6QixNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHTCx3REFBT0E7SUFDckIsTUFBTSxDQUFDTSxPQUFPQyxTQUFTLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNnRCxTQUFTQyxXQUFXLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNrRCxNQUFNQyxRQUFRLEdBQUduRCwrQ0FBUUEsQ0FBVyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ29ELFdBQVdDLGFBQWEsR0FBR3JELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sRUFBRXNELEtBQUssRUFBRSxHQUFHbkQscURBQVFBO0lBQzFCLE1BQU0sRUFBRW9ELEtBQUssRUFBRSxHQUFHM0IsMkRBQVFBO0lBRTFCLE1BQU0sQ0FBQzRCLFdBQVdDLGFBQWEsR0FBR3pELCtDQUFRQSxDQUFpQjtJQUMzRCxNQUFNLENBQUMwRCxjQUFjQyxnQkFBZ0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRELFVBQVVDLFlBQVksR0FBRzdELCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzhELHVCQUF1QkMseUJBQXlCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUNnRSx1QkFBdUJDLHlCQUF5QixHQUFHakUsK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDa0UsY0FBY0MsZ0JBQWdCLEdBQUduRSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNvRSxnQkFBZ0JDLGtCQUFrQixHQUFHckUsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDc0UscUJBQXFCQyx1QkFBdUIsR0FBR3ZFLCtDQUFRQSxDQUFDO0lBRS9ELE1BQU13RSxZQUFZdEUsNkNBQU1BLENBQTBCO0lBRWxELHFDQUFxQztJQUNyQyxNQUFNdUUsd0JBQXdCLENBQUNmO1FBQzdCQyxnQkFBZ0JEO0lBQ2xCO0lBRUF6RCxnREFBU0E7Z0NBQUM7WUFDUixJQUFJeUMsTUFBTTtnQkFDUkssU0FBU0wsS0FBS0ksS0FBSztnQkFDbkJHLFdBQVdQLEtBQUtNLE9BQU87Z0JBQ3ZCRyxRQUFRdUIsTUFBTUMsT0FBTyxDQUFDakMsS0FBS1EsSUFBSSxJQUFJUixLQUFLUSxJQUFJLEdBQUcsRUFBRTtnQkFDakRHLGFBQWE7WUFDZixPQUFPO2dCQUNMTixTQUFTO2dCQUNURSxXQUFXO2dCQUNYRSxRQUFRLEVBQUU7Z0JBQ1ZFLGFBQWE7WUFDZjtRQUNGOytCQUFHO1FBQUNYO0tBQUs7SUFFVCxNQUFNa0MsYUFBYTtRQUNqQmpDLE9BQU9ELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW1DLEVBQUUsS0FBSSxNQUFNL0IsT0FBT0UsU0FBU0U7UUFDekNHLGFBQWE7SUFDZjtJQUVBLE1BQU15QixlQUFlO1FBQ25CLElBQUlwQyxNQUFNO1lBQ1JFLFNBQVNGLEtBQUttQyxFQUFFO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNRSxpQkFBaUIsT0FDckJDLFFBQ0FDO1FBRUF4QixhQUFhdUI7UUFDYixJQUFJO1lBQ0YsSUFBSUU7WUFDSixPQUFRRjtnQkFDTixLQUFLO29CQUNIbkIsWUFBWSxLQUFLLFVBQVU7b0JBQzNCRSx5QkFBeUI7b0JBRXpCLG1DQUFtQztvQkFDbkNvQixRQUFRQyxHQUFHLENBQUM7b0JBQ1p0RCxpRUFBZUEsQ0FBQ2tCLFNBQVMsQ0FBQ3FDO3dCQUN4QkYsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QkM7d0JBQ3pDeEIsWUFBWSxDQUFDeUIsT0FBU0EsT0FBT0Q7b0JBQy9CLEdBQUdFLEtBQUssQ0FBQyxDQUFDQzt3QkFDUkwsUUFBUUssS0FBSyxDQUFDLDhCQUE4QkE7d0JBQzVDakMsTUFBTTs0QkFDSlQsT0FBT0QsRUFBRTs0QkFDVDRDLGFBQWFELGlCQUFpQkUsUUFBUUYsTUFBTUcsT0FBTyxHQUFHOUMsRUFBRTs0QkFDeEQrQyxTQUFTO3dCQUNYO29CQUNGO29CQUNBO2dCQUNGLEtBQUs7b0JBQ0gsSUFBSUMsY0FBYztvQkFDbEJWLFFBQVFDLEdBQUcsQ0FBQztvQkFFWixNQUFNckQscUVBQW1CQSxDQUFDaUIsU0FBUyxDQUFDcUM7d0JBQ2xDRixRQUFRQyxHQUFHLENBQUMseUJBQXlCQzt3QkFDckNRLGVBQWVSO29CQUNqQixHQUFHRSxLQUFLLENBQUMsQ0FBQ0M7d0JBQ1JMLFFBQVFLLEtBQUssQ0FBQywwQkFBMEJBO3dCQUN4Q2pDLE1BQU07NEJBQ0pULE9BQU9ELEVBQUU7NEJBQ1Q0QyxhQUFhRCxpQkFBaUJFLFFBQVFGLE1BQU1HLE9BQU8sR0FBRzlDLEVBQUU7NEJBQ3hEK0MsU0FBUzt3QkFDWDt3QkFDQSxNQUFNSjtvQkFDUjtvQkFFQW5CLGtCQUFrQndCLFlBQVlDLElBQUk7b0JBQ2xDdkIsdUJBQXVCO29CQUN2QjtnQkFDRixLQUFLO29CQUNILElBQUksQ0FBQ2IsY0FBYzt3QkFDakJILE1BQU07NEJBQ0pULE9BQU9ELEVBQUU7NEJBQ1Q0QyxhQUFhNUMsRUFBRTs0QkFDZitDLFNBQVM7d0JBQ1g7d0JBQ0E7b0JBQ0Y7b0JBQ0EvQixZQUFZLEtBQUssVUFBVTtvQkFDM0JFLHlCQUF5QjtvQkFFekIsbUNBQW1DO29CQUNuQ29CLFFBQVFDLEdBQUcsQ0FBQztvQkFDWnBELDhEQUFZQSxDQUFDMEIsY0FBYyxDQUFDMkI7d0JBQzFCRixRQUFRQyxHQUFHLENBQUMsMEJBQTBCQzt3QkFDdEN4QixZQUFZLENBQUN5QixPQUFTQSxPQUFPRDtvQkFDL0IsR0FBR0UsS0FBSyxDQUFDLENBQUNDO3dCQUNSTCxRQUFRSyxLQUFLLENBQUMsMkJBQTJCQTt3QkFDekNqQyxNQUFNOzRCQUNKVCxPQUFPRCxFQUFFOzRCQUNUNEMsYUFBYUQsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc5QyxFQUFFOzRCQUN4RCtDLFNBQVM7d0JBQ1g7b0JBQ0Y7b0JBQ0E7Z0JBQ0YsS0FBSztvQkFDSCxJQUFJLENBQUNYLFdBQVcsQ0FBQ0EsUUFBUWEsSUFBSSxJQUFJO3dCQUMvQnZDLE1BQU07NEJBQ0pULE9BQU9ELEVBQUU7NEJBQ1Q0QyxhQUFhNUMsRUFBRTs0QkFDZitDLFNBQVM7d0JBQ1g7d0JBQ0E7b0JBQ0Y7b0JBQ0EvQixZQUFZLEtBQUssVUFBVTtvQkFDM0JJLHlCQUF5QjtvQkFDekJGLHlCQUF5QjtvQkFFekIsbUNBQW1DO29CQUNuQ29CLFFBQVFDLEdBQUcsQ0FBQywyQ0FBMkNIO29CQUN2RHBELHFFQUFtQkEsQ0FBQ29ELFNBQVMsQ0FBQ0k7d0JBQzVCRixRQUFRQyxHQUFHLENBQUMsMEJBQTBCQzt3QkFDdEN4QixZQUFZLENBQUN5QixPQUFTQSxPQUFPRDtvQkFDL0IsR0FBR0UsS0FBSyxDQUFDLENBQUNDO3dCQUNSTCxRQUFRSyxLQUFLLENBQUMsMkJBQTJCQTt3QkFDekNqQyxNQUFNOzRCQUNKVCxPQUFPRCxFQUFFOzRCQUNUNEMsYUFBYUQsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc5QyxFQUFFOzRCQUN4RCtDLFNBQVM7d0JBQ1g7b0JBQ0Y7b0JBQ0E7Z0JBQ0YsS0FBSztvQkFDSCxJQUFJLENBQUNsQyxjQUFjO3dCQUNqQkgsTUFBTTs0QkFDSlQsT0FBT0QsRUFBRTs0QkFDVDRDLGFBQWE1QyxFQUFFOzRCQUNmK0MsU0FBUzt3QkFDWDt3QkFDQTtvQkFDRjtvQkFDQS9CLFlBQVksS0FBSyxVQUFVO29CQUMzQkUseUJBQXlCO29CQUV6QixtQ0FBbUM7b0JBQ25Db0IsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q0g7b0JBQ3hEaEQsa0VBQWdCQSxDQUFDeUIsY0FBY3VCLFNBQVMsQ0FBQ0k7d0JBQ3ZDRixRQUFRQyxHQUFHLENBQUMsOEJBQThCQzt3QkFDMUN4QixZQUFZLENBQUN5QixPQUFTQSxPQUFPRDtvQkFDL0IsR0FBR0UsS0FBSyxDQUFDLENBQUNDO3dCQUNSTCxRQUFRSyxLQUFLLENBQUMsK0JBQStCQTt3QkFDN0NqQyxNQUFNOzRCQUNKVCxPQUFPRCxFQUFFOzRCQUNUNEMsYUFBYUQsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc5QyxFQUFFOzRCQUN4RCtDLFNBQVM7d0JBQ1g7b0JBQ0Y7b0JBQ0E7Z0JBQ0YsS0FBSztvQkFDSCxJQUFJLENBQUM1QyxRQUFROEMsSUFBSSxJQUFJO3dCQUNuQnZDLE1BQU07NEJBQ0pULE9BQU9ELEVBQUU7NEJBQ1Q0QyxhQUFhNUMsRUFBRTs0QkFDZitDLFNBQVM7d0JBQ1g7d0JBQ0E7b0JBQ0Y7b0JBQ0EsSUFBSTt3QkFDRlQsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLE1BQU1XLFVBQVUsTUFBTTdELDhEQUFZQSxDQUFDYzt3QkFDbkMsTUFBTWdELGFBQWE7K0JBQUksSUFBSUMsSUFBSTttQ0FBSS9DO21DQUFTNkM7NkJBQVE7eUJBQUU7d0JBQ3RENUMsUUFBUTZDO3dCQUNSekMsTUFBTTs0QkFDSlQsT0FBT0QsRUFBRTs0QkFDVDRDLGFBQWE1QyxFQUFFOzRCQUNmK0MsU0FBUzt3QkFDWDtvQkFDRixFQUFFLE9BQU9KLE9BQU87d0JBQ2RMLFFBQVFLLEtBQUssQ0FBQyx5QkFBeUJBO3dCQUN2Q2pDLE1BQU07NEJBQ0pULE9BQU9ELEVBQUU7NEJBQ1Q0QyxhQUFhRCxpQkFBaUJFLFFBQVFGLE1BQU1HLE9BQU8sR0FBRzlDLEVBQUU7NEJBQ3hEK0MsU0FBUzt3QkFDWDtvQkFDRjtvQkFDQTtZQUNKO1FBQ0YsRUFBRSxPQUFPSixPQUFnQjtZQUN2QixNQUFNRyxVQUNKSCxpQkFBaUJFLFFBQVFGLE1BQU1HLE9BQU8sR0FBRzlDLEVBQUU7WUFDN0NVLE1BQU07Z0JBQ0pULE9BQU9ELEVBQUU7Z0JBQ1Q0QyxhQUFhRTtnQkFDYkMsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNSbkMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNeUMscUJBQXFCO1FBQ3pCakQsV0FBVyxHQUF3QlcsT0FBckJaLFNBQVEsZUFBc0IsT0FBVFk7UUFDbkNHLHlCQUF5QjtJQUMzQjtJQUVBLE1BQU1vQyxzQkFBc0I7UUFDMUJsRCxXQUFXRCxRQUFRb0QsT0FBTyxDQUFDMUMsY0FBY0U7UUFDekNHLHlCQUF5QjtJQUMzQjtJQUVBLHFCQUNFOzswQkFDRSw4REFBQ3NDO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNabEQsMEJBQ0MsOERBQUMvQyx1REFBS0E7NENBQ0prRyxPQUFPekQ7NENBQ1AwRCxVQUFVLENBQUNDLElBQU0xRCxTQUFTMEQsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUN4Q0ksYUFBYTlELEVBQUU7NENBQ2Z5RCxXQUFVOzs7OztpRUFHWiw4REFBQ007NENBQUdOLFdBQVU7c0RBQXlCeEQ7Ozs7Ozs7Ozs7O2tEQUczQyw4REFBQ3VEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzdGLHNFQUFZQTs7a0VBQ1gsOERBQUNJLDZFQUFtQkE7d0RBQUNnRyxPQUFPO2tFQUMxQiw0RUFBQ3pHLHlEQUFNQTs0REFBQ3dGLFNBQVE7NERBQVVrQixNQUFLOzREQUFLQyxVQUFVLENBQUMsQ0FBQ3ZEOztnRUFDN0NBLDBCQUNDLDhEQUFDbEIsOEdBQU9BO29FQUFDZ0UsV0FBVTs7Ozs7eUZBRW5CLDhEQUFDakUsOEdBQVFBO29FQUFDaUUsV0FBVTs7Ozs7O2dFQUVyQnpELEVBQUU7Ozs7Ozs7Ozs7OztrRUFHUCw4REFBQ25DLDZFQUFtQkE7OzBFQUNsQiw4REFBQ0MsMEVBQWdCQTtnRUFBQ3FHLFNBQVMsSUFBTWpDLGVBQWU7MEVBQzdDbEMsRUFBRTs7Ozs7OzBFQUVMLDhEQUFDbEMsMEVBQWdCQTtnRUFDZnFHLFNBQVMsSUFBTWpDLGVBQWU7MEVBRTdCbEMsRUFBRTs7Ozs7OzBFQUVMLDhEQUFDakMsK0VBQXFCQTs7Ozs7MEVBQ3RCLDhEQUFDRCwwRUFBZ0JBO2dFQUNmcUcsU0FBUyxJQUFNakMsZUFBZTtnRUFDOUJnQyxVQUFVLENBQUNyRDswRUFFVmIsRUFBRTs7Ozs7OzBFQUVMLDhEQUFDbEMsMEVBQWdCQTtnRUFDZnFHLFNBQVMsSUFBTS9DLHlCQUF5QjswRUFFdkNwQixFQUFFOzs7Ozs7MEVBRUwsOERBQUNsQywwRUFBZ0JBO2dFQUNmcUcsU0FBUyxJQUFNakMsZUFBZSxjQUFjO2dFQUM1Q2dDLFVBQVUsQ0FBQ3JEOzBFQUVWYixFQUFFOzs7Ozs7MEVBRUwsOERBQUNsQywwRUFBZ0JBO2dFQUNmcUcsU0FBUyxJQUFNakMsZUFBZSxjQUFjO2dFQUM1Q2dDLFVBQVUsQ0FBQ3JEOzBFQUVWYixFQUFFOzs7Ozs7MEVBRUwsOERBQUNqQywrRUFBcUJBOzs7OzswRUFDdEIsOERBQUNELDBFQUFnQkE7Z0VBQ2ZxRyxTQUFTLElBQU1qQyxlQUFlO2dFQUM5QmdDLFVBQVUsQ0FBQy9ELFFBQVE4QyxJQUFJLE1BQU0sQ0FBQyxDQUFDdEM7MEVBRTlCWCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NENBS1JPLDBCQUNDLDhEQUFDaEQseURBQU1BO2dEQUFDNEcsU0FBU3BDO2dEQUFZa0MsTUFBSztnREFBS2xCLFNBQVE7O2tFQUM3Qyw4REFBQ3pELDhHQUFJQTt3REFBQ21FLFdBQVU7Ozs7OztvREFDZnpELEVBQUU7Ozs7OztxRUFHTCw4REFBQ3pDLHlEQUFNQTtnREFDTDRHLFNBQVMsSUFBTTNELGFBQWE7Z0RBQzVCeUQsTUFBSztnREFDTGxCLFNBQVE7O2tFQUVSLDhEQUFDckQsOEdBQUlBO3dEQUFDK0QsV0FBVTs7Ozs7O29EQUNmekQsRUFBRTs7Ozs7Ozs0Q0FHTkgsc0JBQ0MsOERBQUN0Qyx5REFBTUE7Z0RBQUN3RixTQUFRO2dEQUFjb0IsU0FBU2xDO2dEQUFjZ0MsTUFBSzs7a0VBQ3hELDhEQUFDMUUsOEdBQU1BO3dEQUFDa0UsV0FBVTs7Ozs7O29EQUNqQnpELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS1gsOERBQUN3RDtnQ0FBSUMsV0FBVTswQ0FDWmxELDBCQUNDLDhEQUFDOUMsNkRBQVFBO29DQUNQaUcsT0FBT3JEO29DQUNQc0QsVUFBVXJEO29DQUNWd0QsYUFBYTlELEVBQUU7Ozs7O3lEQUdqQiw4REFBQ3dEO29DQUFJQyxXQUFVOzhDQUNaNUIsTUFBTUMsT0FBTyxDQUFDekIsU0FDYkEsS0FBSytELEdBQUcsQ0FBQyxDQUFDQyxvQkFDUiw4REFBQ2I7NENBRUNDLFdBQVU7c0RBRVRZOzJDQUhJQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVVuQiw4REFBQ2I7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUMvRiw2REFBb0JBOzRCQUNuQjRHLEtBQUszQzs0QkFDTCtCLE9BQU92RDs0QkFDUHdELFVBQVUsQ0FBQ0QsUUFBVXRELFdBQVdzRCxTQUFTOzRCQUN6Q2EsU0FBU2hFLFlBQVksU0FBUzs0QkFDOUJpRSxhQUFhLENBQUNqRTs0QkFDZGtELFdBQVU7NEJBQ1ZnQixtQkFBbUI3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXpCLDhEQUFDM0QseURBQU1BO2dCQUNMeUcsTUFBTXpEO2dCQUNOMEQsY0FBY3pEOzBCQUVkLDRFQUFDaEQsZ0VBQWFBOztzQ0FDWiw4REFBQ0MsK0RBQVlBO3NDQUNYLDRFQUFDQyw4REFBV0E7MENBQUU0QixFQUFFOzs7Ozs7Ozs7OztzQ0FFbEIsOERBQUN3RDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzlGLG1FQUFlQTtnQ0FBQ2lILFFBQVE3RDs7Ozs7Ozs7Ozs7c0NBRTNCLDhEQUFDMUMsK0RBQVlBOzs4Q0FDWCw4REFBQ2QseURBQU1BO29DQUNMd0YsU0FBUTtvQ0FDUm9CLFNBQVMsSUFBTWpELHlCQUF5Qjs4Q0FFdkNsQixFQUFFOzs7Ozs7OENBRUwsOERBQUN6Qyx5REFBTUE7b0NBQUM0RyxTQUFTZDtvQ0FBb0JOLFNBQVE7OENBQWEvQyxFQUFFOzs7Ozs7Z0NBQzNEYSw4QkFDQyw4REFBQ3RELHlEQUFNQTtvQ0FBQzRHLFNBQVNiO29DQUFxQlAsU0FBUTs4Q0FBYS9DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9yRSw4REFBQy9CLHlEQUFNQTtnQkFDTHlHLE1BQU12RDtnQkFDTndELGNBQWN2RDswQkFFZCw0RUFBQ2xELGdFQUFhQTs7c0NBQ1osOERBQUNDLCtEQUFZQTtzQ0FDWCw0RUFBQ0MsOERBQVdBOzBDQUFFNEIsRUFBRTs7Ozs7Ozs7Ozs7c0NBRWxCLDhEQUFDbEIsOERBQVFBOzRCQUNQZ0YsYUFBYTlELEVBQUU7NEJBQ2YwRCxPQUFPckM7NEJBQ1BzQyxVQUFVLENBQUNDLElBQThDdEMsZ0JBQWdCc0MsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRCQUN2RlEsVUFBVXZELGNBQWM7Ozs7OztzQ0FFMUIsOERBQUN0QywrREFBWUE7OzhDQUNYLDhEQUFDZCx5REFBTUE7b0NBQ0x3RixTQUFRO29DQUNSb0IsU0FBUyxJQUFNL0MseUJBQXlCO29DQUN4QzhDLFVBQVV2RCxjQUFjOzhDQUV2QlgsRUFBRTs7Ozs7OzhDQUVMLDhEQUFDekMseURBQU1BO29DQUNMNEcsU0FBUyxJQUFNakMsZUFBZSxVQUFVYjtvQ0FDeEMwQixTQUFRO29DQUNSbUIsVUFBVSxDQUFDN0MsYUFBYTRCLElBQUksTUFBTXRDLGNBQWM7OENBRS9DQSxjQUFjLHlCQUNiOzswREFDRSw4REFBQ2xCLDhHQUFPQTtnREFBQ2dFLFdBQVU7Ozs7Ozs0Q0FDbEJ6RCxFQUFFOzt1REFHTEEsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVosOERBQUMxQixxRUFBV0E7Z0JBQ1ZvRyxNQUFNakQ7Z0JBQ05rRCxjQUFjakQ7MEJBRWQsNEVBQUNqRCw0RUFBa0JBOztzQ0FDakIsOERBQUNHLDJFQUFpQkE7OzhDQUNoQiw4REFBQ0MsMEVBQWdCQTs4Q0FBRW1CLEVBQUU7Ozs7Ozs4Q0FDckIsOERBQUN0QixnRkFBc0JBOzhDQUNwQnNCLEVBQUU7Ozs7Ozs7Ozs7OztzQ0FHUCw4REFBQ3dEOzRCQUFJQyxXQUFVO3NDQUNabEM7Ozs7OztzQ0FFSCw4REFBQzVDLDJFQUFpQkE7OzhDQUNoQiw4REFBQ0gsMkVBQWlCQTs4Q0FBRXdCLEVBQUU7Ozs7Ozs4Q0FDdEIsOERBQUN6QiwyRUFBaUJBO29DQUNoQjRGLFNBQVM7d0NBQ1BqRSxTQUFTcUI7d0NBQ1RHLHVCQUF1QjtvQ0FDekI7b0NBQ0ErQixXQUFVOzhDQUVUekQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9qQjtHQXhjZ0JKOztRQUNBRCxvREFBT0E7UUFLSHJDLGlEQUFRQTtRQUNSeUIsdURBQVFBOzs7S0FQWmEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW50aG9cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcbXlub3RlXFxyZW5kZXJlclxcc3JjXFxjb21wb25lbnRzXFxOb3RlRWRpdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIgXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwibmV4dC10aGVtZXNcIjtcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XHJcbmltcG9ydCB7IFRhZ0lucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9UYWdJbnB1dFwiO1xyXG5pbXBvcnQgQ2hlcnJ5TWFya2Rvd25FZGl0b3IsIHsgQ2hlcnJ5TWFya2Rvd25FZGl0b3JSZWYgfSBmcm9tIFwiLi9DaGVycnlNYXJrZG93bkVkaXRvclwiO1xyXG5pbXBvcnQgTWFya2Rvd25QcmV2aWV3IGZyb20gXCJAdWl3L3JlYWN0LW1hcmtkb3duLXByZXZpZXdcIjtcclxuaW1wb3J0IHtcclxuICBEcm9wZG93bk1lbnUsXHJcbiAgRHJvcGRvd25NZW51Q29udGVudCxcclxuICBEcm9wZG93bk1lbnVJdGVtLFxyXG4gIERyb3Bkb3duTWVudVNlcGFyYXRvcixcclxuICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxyXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudVwiO1xyXG5pbXBvcnQge1xyXG4gIERpYWxvZyxcclxuICBEaWFsb2dDb250ZW50LFxyXG4gIERpYWxvZ0hlYWRlcixcclxuICBEaWFsb2dUaXRsZSxcclxuICBEaWFsb2dGb290ZXIsXHJcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIjtcclxuaW1wb3J0IHtcclxuICBBbGVydERpYWxvZyxcclxuICBBbGVydERpYWxvZ0FjdGlvbixcclxuICBBbGVydERpYWxvZ0NhbmNlbCxcclxuICBBbGVydERpYWxvZ0NvbnRlbnQsXHJcbiAgQWxlcnREaWFsb2dEZXNjcmlwdGlvbixcclxuICBBbGVydERpYWxvZ0Zvb3RlcixcclxuICBBbGVydERpYWxvZ0hlYWRlcixcclxuICBBbGVydERpYWxvZ1RpdGxlLFxyXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYWxlcnQtZGlhbG9nXCI7XHJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiO1xyXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiO1xyXG5pbXBvcnQge1xyXG4gIHN1bW1hcml6ZSxcclxuICBnZW5lcmF0ZVRpdGxlLFxyXG4gIHBvbGlzaFdpdGhPcGVuUm91dGVyLFxyXG4gIGV4cGFuZENvbnRlbnQsXHJcbiAgZXhwYW5kQ29udGVudFN0cmVhbSxcclxuICBzdW1tYXJpemVTdHJlYW0sXHJcbiAgZ2VuZXJhdGVUaXRsZVN0cmVhbSxcclxuICBwb2xpc2hTdHJlYW0sXHJcbiAgY2hhbmdlVG9uZVN0cmVhbSxcclxuICBjaGFuZ2VUb25lV2l0aE9wZW5Sb3V0ZXIsXHJcbiAgZ2VuZXJhdGVUYWdzLFxyXG59IGZyb20gXCJAL2xpYi9haS1zZXJ2aWNlXCI7XHJcbmltcG9ydCB7IFNhdmUsIFRyYXNoMiwgU3BhcmtsZXMsIExvYWRlcjIsIEVkaXQgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZUkxOG4gfSBmcm9tIFwiQC9jb250ZXh0cy9pMThuXCI7XHJcblxyXG5pbnRlcmZhY2UgTm90ZSB7XHJcbiAgaWQ6IHN0cmluZztcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIGNvbnRlbnQ6IHN0cmluZztcclxuICB0YWdzOiBzdHJpbmdbXTtcclxufVxyXG5cclxuaW50ZXJmYWNlIE5vdGVFZGl0b3JQcm9wcyB7XHJcbiAgbm90ZTogTm90ZSB8IG51bGw7XHJcbiAgb25TYXZlOiAoXHJcbiAgICBpZDogc3RyaW5nIHwgbnVsbCxcclxuICAgIHRpdGxlOiBzdHJpbmcsXHJcbiAgICBjb250ZW50OiBzdHJpbmcsXHJcbiAgICB0YWdzOiBzdHJpbmdbXVxyXG4gICkgPT4gdm9pZDtcclxuICBvbkRlbGV0ZTogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XHJcbn1cclxuXHJcbnR5cGUgQWlMb2FkaW5nU3RhdGUgPVxyXG4gIHwgZmFsc2VcclxuICB8IFwic3VtbWFyaXplXCJcclxuICB8IFwiZ2VuZXJhdGVUaXRsZVwiXHJcbiAgfCBcInBvbGlzaFwiXHJcbiAgfCBcImV4cGFuZFwiXHJcbiAgfCBcImNoYW5nZVRvbmVcIlxyXG4gIHwgXCJnZW5lcmF0ZVRhZ3NcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBOb3RlRWRpdG9yKHsgbm90ZSwgb25TYXZlLCBvbkRlbGV0ZSB9OiBOb3RlRWRpdG9yUHJvcHMpIHtcclxuICBjb25zdCB7IHQgfSA9IHVzZUkxOG4oKTtcclxuICBjb25zdCBbdGl0bGUsIHNldFRpdGxlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtjb250ZW50LCBzZXRDb250ZW50XSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFt0YWdzLCBzZXRUYWdzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XHJcbiAgY29uc3QgW2lzRWRpdGluZywgc2V0SXNFZGl0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB7IHRoZW1lIH0gPSB1c2VUaGVtZSgpO1xyXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XHJcblxyXG4gIGNvbnN0IFthaUxvYWRpbmcsIHNldEFpTG9hZGluZ10gPSB1c2VTdGF0ZTxBaUxvYWRpbmdTdGF0ZT4oZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFRleHQsIHNldFNlbGVjdGVkVGV4dF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbYWlSZXN1bHQsIHNldEFpUmVzdWx0XSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpc1Jlc3VsdERpYWxvZ1Zpc2libGUsIHNldElzUmVzdWx0RGlhbG9nVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzRXhwYW5kRGlhbG9nVmlzaWJsZSwgc2V0SXNFeHBhbmREaWFsb2dWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZXhwYW5kUHJvbXB0LCBzZXRFeHBhbmRQcm9tcHRdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3N1Z2dlc3RlZFRpdGxlLCBzZXRTdWdnZXN0ZWRUaXRsZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNUaXRsZUFsZXJ0VmlzaWJsZSwgc2V0SXNUaXRsZUFsZXJ0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IGVkaXRvclJlZiA9IHVzZVJlZjxDaGVycnlNYXJrZG93bkVkaXRvclJlZj4obnVsbCk7XHJcblxyXG4gIC8vIOmBuOWPluaWh+Wtl+iZleeQhuWwh+eUsSBDaGVycnlNYXJrZG93bkVkaXRvciDlhYPku7bomZXnkIZcclxuICBjb25zdCBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UgPSAoc2VsZWN0ZWRUZXh0OiBzdHJpbmcpID0+IHtcclxuICAgIHNldFNlbGVjdGVkVGV4dChzZWxlY3RlZFRleHQpO1xyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAobm90ZSkge1xyXG4gICAgICBzZXRUaXRsZShub3RlLnRpdGxlKTtcclxuICAgICAgc2V0Q29udGVudChub3RlLmNvbnRlbnQpO1xyXG4gICAgICBzZXRUYWdzKEFycmF5LmlzQXJyYXkobm90ZS50YWdzKSA/IG5vdGUudGFncyA6IFtdKTtcclxuICAgICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldFRpdGxlKFwiXCIpO1xyXG4gICAgICBzZXRDb250ZW50KFwiXCIpO1xyXG4gICAgICBzZXRUYWdzKFtdKTtcclxuICAgICAgc2V0SXNFZGl0aW5nKHRydWUpO1xyXG4gICAgfVxyXG4gIH0sIFtub3RlXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSAoKSA9PiB7XHJcbiAgICBvblNhdmUobm90ZT8uaWQgfHwgbnVsbCwgdGl0bGUsIGNvbnRlbnQsIHRhZ3MpO1xyXG4gICAgc2V0SXNFZGl0aW5nKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGUgPSAoKSA9PiB7XHJcbiAgICBpZiAobm90ZSkge1xyXG4gICAgICBvbkRlbGV0ZShub3RlLmlkKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVBaUFjdGlvbiA9IGFzeW5jIChcclxuICAgIGFjdGlvbjogQWlMb2FkaW5nU3RhdGUsXHJcbiAgICBwYXlsb2FkPzogYW55XHJcbiAgKTogUHJvbWlzZTx2b2lkPiA9PiB7XHJcbiAgICBzZXRBaUxvYWRpbmcoYWN0aW9uKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGxldCByZXN1bHQ6IHN0cmluZztcclxuICAgICAgc3dpdGNoIChhY3Rpb24pIHtcclxuICAgICAgICBjYXNlIFwic3VtbWFyaXplXCI6XHJcbiAgICAgICAgICBzZXRBaVJlc3VsdChcIlwiKTsgLy8g5riF56m65LmL5YmN55qE57WQ5p6cXHJcbiAgICAgICAgICBzZXRJc1Jlc3VsdERpYWxvZ1Zpc2libGUodHJ1ZSk7XHJcblxyXG4gICAgICAgICAgLy8g56uL5Y2z5omT6ZaLIHJlc3VsdCBkaWFsb2cg5Lim6ZaL5aeLIHN0cmVhbWluZ1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCJTdGFydGluZyBzdW1tYXJpemUgc3RyZWFtaW5nXCIpO1xyXG4gICAgICAgICAgc3VtbWFyaXplU3RyZWFtKGNvbnRlbnQsIChjaHVuaykgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIlJlY2VpdmVkIHN1bW1hcml6ZSBjaHVuazpcIiwgY2h1bmspO1xyXG4gICAgICAgICAgICBzZXRBaVJlc3VsdCgocHJldikgPT4gcHJldiArIGNodW5rKTtcclxuICAgICAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiU3VtbWFyaXplIHN0cmVhbWluZyBlcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgdGl0bGU6IHQoXCJlZGl0b3IuYWlfb3BlcmF0aW9uX2ZhaWxlZFwiKSxcclxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiB0KFwiZWRpdG9yLnVua25vd25fZXJyb3JcIiksXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSBcImdlbmVyYXRlVGl0bGVcIjpcclxuICAgICAgICAgIGxldCB0aXRsZVJlc3VsdCA9IFwiXCI7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlN0YXJ0aW5nIHRpdGxlIGdlbmVyYXRpb24gc3RyZWFtaW5nXCIpO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBhd2FpdCBnZW5lcmF0ZVRpdGxlU3RyZWFtKGNvbnRlbnQsIChjaHVuaykgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIlJlY2VpdmVkIHRpdGxlIGNodW5rOlwiLCBjaHVuayk7XHJcbiAgICAgICAgICAgIHRpdGxlUmVzdWx0ICs9IGNodW5rO1xyXG4gICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJUaXRsZSBzdHJlYW1pbmcgZXJyb3I6XCIsIGVycm9yKTtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB0KFwiZWRpdG9yLmFpX29wZXJhdGlvbl9mYWlsZWRcIiksXHJcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogdChcImVkaXRvci51bmtub3duX2Vycm9yXCIpLFxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIHNldFN1Z2dlc3RlZFRpdGxlKHRpdGxlUmVzdWx0LnRyaW0oKSk7XHJcbiAgICAgICAgICBzZXRJc1RpdGxlQWxlcnRWaXNpYmxlKHRydWUpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSBcInBvbGlzaFwiOlxyXG4gICAgICAgICAgaWYgKCFzZWxlY3RlZFRleHQpIHtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB0KFwiZWRpdG9yLnNlbGVjdGlvbl9lcnJvclwiKSxcclxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdChcImVkaXRvci5zZWxlY3RfdGV4dF9maXJzdFwiKSxcclxuICAgICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBzZXRBaVJlc3VsdChcIlwiKTsgLy8g5riF56m65LmL5YmN55qE57WQ5p6cXHJcbiAgICAgICAgICBzZXRJc1Jlc3VsdERpYWxvZ1Zpc2libGUodHJ1ZSk7XHJcblxyXG4gICAgICAgICAgLy8g56uL5Y2z5omT6ZaLIHJlc3VsdCBkaWFsb2cg5Lim6ZaL5aeLIHN0cmVhbWluZ1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCJTdGFydGluZyBwb2xpc2ggc3RyZWFtaW5nXCIpO1xyXG4gICAgICAgICAgcG9saXNoU3RyZWFtKHNlbGVjdGVkVGV4dCwgKGNodW5rKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiUmVjZWl2ZWQgcG9saXNoIGNodW5rOlwiLCBjaHVuayk7XHJcbiAgICAgICAgICAgIHNldEFpUmVzdWx0KChwcmV2KSA9PiBwcmV2ICsgY2h1bmspO1xyXG4gICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJQb2xpc2ggc3RyZWFtaW5nIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICB0aXRsZTogdChcImVkaXRvci5haV9vcGVyYXRpb25fZmFpbGVkXCIpLFxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IHQoXCJlZGl0b3IudW5rbm93bl9lcnJvclwiKSxcclxuICAgICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlIFwiZXhwYW5kXCI6XHJcbiAgICAgICAgICBpZiAoIXBheWxvYWQgfHwgIXBheWxvYWQudHJpbSgpKSB7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICB0aXRsZTogdChcImVkaXRvci5pbnB1dF9lcnJvclwiKSxcclxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdChcImVkaXRvci5lbnRlcl92YWxpZF9wcm9tcHRcIiksXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgc2V0QWlSZXN1bHQoXCJcIik7IC8vIOa4heepuuS5i+WJjeeahOe1kOaenFxyXG4gICAgICAgICAgc2V0SXNFeHBhbmREaWFsb2dWaXNpYmxlKGZhbHNlKTtcclxuICAgICAgICAgIHNldElzUmVzdWx0RGlhbG9nVmlzaWJsZSh0cnVlKTtcclxuXHJcbiAgICAgICAgICAvLyDnq4vljbPmiZPplosgcmVzdWx0IGRpYWxvZyDkuKbplovlp4sgc3RyZWFtaW5nXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlN0YXJ0aW5nIGV4cGFuZCBzdHJlYW1pbmcgd2l0aCBwYXlsb2FkOlwiLCBwYXlsb2FkKTtcclxuICAgICAgICAgIGV4cGFuZENvbnRlbnRTdHJlYW0ocGF5bG9hZCwgKGNodW5rKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiUmVjZWl2ZWQgZXhwYW5kIGNodW5rOlwiLCBjaHVuayk7XHJcbiAgICAgICAgICAgIHNldEFpUmVzdWx0KChwcmV2KSA9PiBwcmV2ICsgY2h1bmspO1xyXG4gICAgICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFeHBhbmQgc3RyZWFtaW5nIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICB0aXRsZTogdChcImVkaXRvci5haV9vcGVyYXRpb25fZmFpbGVkXCIpLFxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IHQoXCJlZGl0b3IudW5rbm93bl9lcnJvclwiKSxcclxuICAgICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlIFwiY2hhbmdlVG9uZVwiOlxyXG4gICAgICAgICAgaWYgKCFzZWxlY3RlZFRleHQpIHtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB0KFwiZWRpdG9yLnNlbGVjdGlvbl9lcnJvclwiKSxcclxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdChcImVkaXRvci5zZWxlY3RfdG9uZV90ZXh0X2ZpcnN0XCIpLFxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHNldEFpUmVzdWx0KFwiXCIpOyAvLyDmuIXnqbrkuYvliY3nmoTntZDmnpxcclxuICAgICAgICAgIHNldElzUmVzdWx0RGlhbG9nVmlzaWJsZSh0cnVlKTtcclxuXHJcbiAgICAgICAgICAvLyDnq4vljbPmiZPplosgcmVzdWx0IGRpYWxvZyDkuKbplovlp4sgc3RyZWFtaW5nXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIlN0YXJ0aW5nIGNoYW5nZVRvbmUgc3RyZWFtaW5nIHdpdGggdG9uZTpcIiwgcGF5bG9hZCk7XHJcbiAgICAgICAgICBjaGFuZ2VUb25lU3RyZWFtKHNlbGVjdGVkVGV4dCwgcGF5bG9hZCwgKGNodW5rKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiUmVjZWl2ZWQgY2hhbmdlVG9uZSBjaHVuazpcIiwgY2h1bmspO1xyXG4gICAgICAgICAgICBzZXRBaVJlc3VsdCgocHJldikgPT4gcHJldiArIGNodW5rKTtcclxuICAgICAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiQ2hhbmdlVG9uZSBzdHJlYW1pbmcgZXJyb3I6XCIsIGVycm9yKTtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB0KFwiZWRpdG9yLmFpX29wZXJhdGlvbl9mYWlsZWRcIiksXHJcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogdChcImVkaXRvci51bmtub3duX2Vycm9yXCIpLFxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgXCJnZW5lcmF0ZVRhZ3NcIjpcclxuICAgICAgICAgIGlmICghY29udGVudC50cmltKCkpIHtcclxuICAgICAgICAgICAgdG9hc3Qoe1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB0KFwiZWRpdG9yLmlucHV0X2Vycm9yXCIpLFxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB0KFwiZWRpdG9yLmNvbnRlbnRfcmVxdWlyZWRfZm9yX3RhZ3NcIiksXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJTdGFydGluZyB0YWcgZ2VuZXJhdGlvblwiKTtcclxuICAgICAgICAgICAgY29uc3QgbmV3VGFncyA9IGF3YWl0IGdlbmVyYXRlVGFncyhjb250ZW50KTtcclxuICAgICAgICAgICAgY29uc3QgdW5pcXVlVGFncyA9IFsuLi5uZXcgU2V0KFsuLi50YWdzLCAuLi5uZXdUYWdzXSldO1xyXG4gICAgICAgICAgICBzZXRUYWdzKHVuaXF1ZVRhZ3MpO1xyXG4gICAgICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICAgICAgdGl0bGU6IHQoXCJlZGl0b3IudGFnc19nZW5lcmF0ZWRcIiksXHJcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHQoXCJlZGl0b3IudGFnc19hZGRlZF9zdWNjZXNzZnVsbHlcIiksXHJcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIlRhZyBnZW5lcmF0aW9uIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgICAgICB0aXRsZTogdChcImVkaXRvci5haV9vcGVyYXRpb25fZmFpbGVkXCIpLFxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IHQoXCJlZGl0b3IudW5rbm93bl9lcnJvclwiKSxcclxuICAgICAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XHJcbiAgICAgIGNvbnN0IG1lc3NhZ2UgPVxyXG4gICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogdChcImVkaXRvci51bmtub3duX2Vycm9yXCIpO1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IHQoXCJlZGl0b3IuYWlfb3BlcmF0aW9uX2ZhaWxlZFwiKSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogbWVzc2FnZSxcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0QWlMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVJbnNlcnRSZXN1bHQgPSAoKSA9PiB7XHJcbiAgICBzZXRDb250ZW50KGAke2NvbnRlbnR9XFxuXFxuLS0tXFxuXFxuJHthaVJlc3VsdH1gKTtcclxuICAgIHNldElzUmVzdWx0RGlhbG9nVmlzaWJsZShmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlUmVwbGFjZVJlc3VsdCA9ICgpID0+IHtcclxuICAgIHNldENvbnRlbnQoY29udGVudC5yZXBsYWNlKHNlbGVjdGVkVGV4dCwgYWlSZXN1bHQpKTtcclxuICAgIHNldElzUmVzdWx0RGlhbG9nVmlzaWJsZShmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBiLTZcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAge2lzRWRpdGluZyA/IChcclxuICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17dGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VGl0bGUoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dChcImVkaXRvci50aXRsZV9wbGFjZWhvbGRlclwiKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1hdXRvIG1heC13LTJ4bCBweC0wIHB5LTEgdGV4dC14bCBmb250LXNlbWlib2xkIGJnLXRyYW5zcGFyZW50IGJvcmRlci1ub25lIHNoYWRvdy1ub25lIGZvY3VzLXZpc2libGU6cmluZy0wXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGRcIj57dGl0bGV9PC9oMj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGRpc2FibGVkPXshIWFpTG9hZGluZ30+XHJcbiAgICAgICAgICAgICAgICAgICAge2FpTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cInctNCBoLTQgbXItMiBhbmltYXRlLXNwaW5cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIHt0KFwiZWRpdG9yLmFpX3Rvb2xzXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbSBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBaUFjdGlvbihcInN1bW1hcml6ZVwiKX0+XHJcbiAgICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3Iuc3VtbWFyaXplXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQWlBY3Rpb24oXCJnZW5lcmF0ZVRpdGxlXCIpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3IuZ2VuZXJhdGVfdGl0bGVcIil9XHJcbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxyXG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFpQWN0aW9uKFwicG9saXNoXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRUZXh0fVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3IucG9saXNoX3NlbGVjdGVkXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNFeHBhbmREaWFsb2dWaXNpYmxlKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3IuZXhwYW5kX2NvbnRlbnRcIil9XHJcbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBaUFjdGlvbihcImNoYW5nZVRvbmVcIiwgXCLlsIjmpa1cIil9XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFRleHR9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7dChcImVkaXRvci5jaGFuZ2VfdG9uZV9wcm9mZXNzaW9uYWxcIil9XHJcbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBaUFjdGlvbihcImNoYW5nZVRvbmVcIiwgXCLkvJHplpJcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFRleHR9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7dChcImVkaXRvci5jaGFuZ2VfdG9uZV9jYXN1YWxcIil9XHJcbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVNlcGFyYXRvciAvPlxyXG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFpQWN0aW9uKFwiZ2VuZXJhdGVUYWdzXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY29udGVudC50cmltKCkgfHwgISFhaUxvYWRpbmd9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7dChcImVkaXRvci5nZW5lcmF0ZV90YWdzXCIpfVxyXG4gICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUNvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnU+XHJcblxyXG4gICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVNhdmV9IHNpemU9XCJzbVwiIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIj5cclxuICAgICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3Iuc2F2ZVwiKX1cclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzRWRpdGluZyh0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICB7dChcImVkaXRvci5lZGl0XCIpfVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICB7bm90ZSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAge3QoXCJlZGl0b3IuZGVsZXRlXCIpfVxyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxyXG4gICAgICAgICAgICB7aXNFZGl0aW5nID8gKFxyXG4gICAgICAgICAgICAgIDxUYWdJbnB1dFxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3RhZ3N9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17c2V0VGFnc31cclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KFwiZWRpdG9yLnRhZ3NfcGxhY2Vob2xkZXJcIil9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICB7QXJyYXkuaXNBcnJheSh0YWdzKSAmJlxyXG4gICAgICAgICAgICAgICAgICB0YWdzLm1hcCgodGFnKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXt0YWd9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC1zbSByb3VuZGVkLWZ1bGwgYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHt0YWd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGZsZXgtMSBwdC00XCI+XHJcbiAgICAgICAgICA8Q2hlcnJ5TWFya2Rvd25FZGl0b3JcclxuICAgICAgICAgICAgcmVmPXtlZGl0b3JSZWZ9XHJcbiAgICAgICAgICAgIHZhbHVlPXtjb250ZW50fVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRDb250ZW50KHZhbHVlIHx8IFwiXCIpfVxyXG4gICAgICAgICAgICBwcmV2aWV3PXtpc0VkaXRpbmcgPyBcImVkaXRcIiA6IFwicHJldmlld1wifVxyXG4gICAgICAgICAgICBoaWRlVG9vbGJhcj17IWlzRWRpdGluZ31cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcclxuICAgICAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2U9e2hhbmRsZVNlbGVjdGlvbkNoYW5nZX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIEFJIFJlc3VsdCBEaWFsb2cgKi99XHJcbiAgICAgIDxEaWFsb2dcclxuICAgICAgICBvcGVuPXtpc1Jlc3VsdERpYWxvZ1Zpc2libGV9XHJcbiAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRJc1Jlc3VsdERpYWxvZ1Zpc2libGV9XHJcbiAgICAgID5cclxuICAgICAgICA8RGlhbG9nQ29udGVudD5cclxuICAgICAgICAgIDxEaWFsb2dIZWFkZXI+XHJcbiAgICAgICAgICAgIDxEaWFsb2dUaXRsZT57dChcImVkaXRvci5haV9yZXN1bHRfdGl0bGVcIil9PC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgbXktNCBib3JkZXIgcm91bmRlZC1tZCBiZy1tdXRlZCBtYXgtaC1bNzB2aF0gb3ZlcmZsb3ctYXV0b1wiPlxyXG4gICAgICAgICAgICA8TWFya2Rvd25QcmV2aWV3IHNvdXJjZT17YWlSZXN1bHR9IC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxEaWFsb2dGb290ZXI+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNSZXN1bHREaWFsb2dWaXNpYmxlKGZhbHNlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KFwiZWRpdG9yLmNhbmNlbFwiKX1cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlSW5zZXJ0UmVzdWx0fSB2YXJpYW50PVwic2Vjb25kYXJ5XCI+e3QoXCJlZGl0b3IuaW5zZXJ0X2F0X2VuZFwiKX08L0J1dHRvbj5cclxuICAgICAgICAgICAge3NlbGVjdGVkVGV4dCAmJiAoXHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVSZXBsYWNlUmVzdWx0fSB2YXJpYW50PVwic2Vjb25kYXJ5XCI+e3QoXCJlZGl0b3IucmVwbGFjZV9zZWxlY3RlZFwiKX08L0J1dHRvbj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgPC9EaWFsb2c+XHJcblxyXG4gICAgICB7LyogRXhwYW5kIENvbnRlbnQgRGlhbG9nICovfVxyXG4gICAgICA8RGlhbG9nXHJcbiAgICAgICAgb3Blbj17aXNFeHBhbmREaWFsb2dWaXNpYmxlfVxyXG4gICAgICAgIG9uT3BlbkNoYW5nZT17c2V0SXNFeHBhbmREaWFsb2dWaXNpYmxlfVxyXG4gICAgICA+XHJcbiAgICAgICAgPERpYWxvZ0NvbnRlbnQ+XHJcbiAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgICA8RGlhbG9nVGl0bGU+e3QoXCJlZGl0b3IuZXhwYW5kX2NvbnRlbnRfdGl0bGVcIil9PC9EaWFsb2dUaXRsZT5cclxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgPFRleHRhcmVhXHJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KFwiZWRpdG9yLmV4cGFuZF9wbGFjZWhvbGRlclwiKX1cclxuICAgICAgICAgICAgdmFsdWU9e2V4cGFuZFByb21wdH1cclxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MVGV4dEFyZWFFbGVtZW50PikgPT4gc2V0RXhwYW5kUHJvbXB0KGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgZGlzYWJsZWQ9e2FpTG9hZGluZyA9PT0gXCJleHBhbmRcIn1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzRXhwYW5kRGlhbG9nVmlzaWJsZShmYWxzZSl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FpTG9hZGluZyA9PT0gXCJleHBhbmRcIn1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KFwiZWRpdG9yLmNhbmNlbFwiKX1cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBaUFjdGlvbihcImV4cGFuZFwiLCBleHBhbmRQcm9tcHQpfVxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXshZXhwYW5kUHJvbXB0LnRyaW0oKSB8fCBhaUxvYWRpbmcgPT09IFwiZXhwYW5kXCJ9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7YWlMb2FkaW5nID09PSBcImV4cGFuZFwiID8gKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yIGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIHt0KFwiZWRpdG9yLmdlbmVyYXRpbmdcIil9XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgdChcImVkaXRvci5nZW5lcmF0ZVwiKVxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPC9EaWFsb2dGb290ZXI+XHJcbiAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICA8L0RpYWxvZz5cclxuXHJcbiAgICAgIHsvKiBHZW5lcmF0ZSBUaXRsZSBBbGVydCAqL31cclxuICAgICAgPEFsZXJ0RGlhbG9nXHJcbiAgICAgICAgb3Blbj17aXNUaXRsZUFsZXJ0VmlzaWJsZX1cclxuICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldElzVGl0bGVBbGVydFZpc2libGV9XHJcbiAgICAgID5cclxuICAgICAgICA8QWxlcnREaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgPEFsZXJ0RGlhbG9nSGVhZGVyPlxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dUaXRsZT57dChcImVkaXRvci5zdWdnZXN0ZWRfdGl0bGVcIil9PC9BbGVydERpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dEZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICB7dChcImVkaXRvci5zdWdnZXN0ZWRfdGl0bGVfZGVzY1wiKX1cclxuICAgICAgICAgICAgPC9BbGVydERpYWxvZ0Rlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgPC9BbGVydERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGZvbnQtc2VtaWJvbGQgYm9yZGVyIHJvdW5kZWQtbWQgYmctbXV0ZWRcIj5cclxuICAgICAgICAgICAge3N1Z2dlc3RlZFRpdGxlfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8QWxlcnREaWFsb2dGb290ZXI+XHJcbiAgICAgICAgICAgIDxBbGVydERpYWxvZ0NhbmNlbD57dChcImVkaXRvci5jYW5jZWxcIil9PC9BbGVydERpYWxvZ0NhbmNlbD5cclxuICAgICAgICAgICAgPEFsZXJ0RGlhbG9nQWN0aW9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgc2V0VGl0bGUoc3VnZ2VzdGVkVGl0bGUpO1xyXG4gICAgICAgICAgICAgICAgc2V0SXNUaXRsZUFsZXJ0VmlzaWJsZShmYWxzZSk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGJnLWJsYWNrIGhvdmVyOmJnLWdyYXktODAwXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0KFwiZWRpdG9yLnJlcGxhY2VcIil9XHJcbiAgICAgICAgICAgIDwvQWxlcnREaWFsb2dBY3Rpb24+XHJcbiAgICAgICAgICA8L0FsZXJ0RGlhbG9nRm9vdGVyPlxyXG4gICAgICAgIDwvQWxlcnREaWFsb2dDb250ZW50PlxyXG4gICAgICA8L0FsZXJ0RGlhbG9nPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVRoZW1lIiwiQnV0dG9uIiwiSW5wdXQiLCJUYWdJbnB1dCIsIkNoZXJyeU1hcmtkb3duRWRpdG9yIiwiTWFya2Rvd25QcmV2aWV3IiwiRHJvcGRvd25NZW51IiwiRHJvcGRvd25NZW51Q29udGVudCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJEcm9wZG93bk1lbnVTZXBhcmF0b3IiLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nRm9vdGVyIiwiQWxlcnREaWFsb2ciLCJBbGVydERpYWxvZ0FjdGlvbiIsIkFsZXJ0RGlhbG9nQ2FuY2VsIiwiQWxlcnREaWFsb2dDb250ZW50IiwiQWxlcnREaWFsb2dEZXNjcmlwdGlvbiIsIkFsZXJ0RGlhbG9nRm9vdGVyIiwiQWxlcnREaWFsb2dIZWFkZXIiLCJBbGVydERpYWxvZ1RpdGxlIiwiVGV4dGFyZWEiLCJ1c2VUb2FzdCIsImV4cGFuZENvbnRlbnRTdHJlYW0iLCJzdW1tYXJpemVTdHJlYW0iLCJnZW5lcmF0ZVRpdGxlU3RyZWFtIiwicG9saXNoU3RyZWFtIiwiY2hhbmdlVG9uZVN0cmVhbSIsImdlbmVyYXRlVGFncyIsIlNhdmUiLCJUcmFzaDIiLCJTcGFya2xlcyIsIkxvYWRlcjIiLCJFZGl0IiwidXNlSTE4biIsIk5vdGVFZGl0b3IiLCJub3RlIiwib25TYXZlIiwib25EZWxldGUiLCJ0IiwidGl0bGUiLCJzZXRUaXRsZSIsImNvbnRlbnQiLCJzZXRDb250ZW50IiwidGFncyIsInNldFRhZ3MiLCJpc0VkaXRpbmciLCJzZXRJc0VkaXRpbmciLCJ0aGVtZSIsInRvYXN0IiwiYWlMb2FkaW5nIiwic2V0QWlMb2FkaW5nIiwic2VsZWN0ZWRUZXh0Iiwic2V0U2VsZWN0ZWRUZXh0IiwiYWlSZXN1bHQiLCJzZXRBaVJlc3VsdCIsImlzUmVzdWx0RGlhbG9nVmlzaWJsZSIsInNldElzUmVzdWx0RGlhbG9nVmlzaWJsZSIsImlzRXhwYW5kRGlhbG9nVmlzaWJsZSIsInNldElzRXhwYW5kRGlhbG9nVmlzaWJsZSIsImV4cGFuZFByb21wdCIsInNldEV4cGFuZFByb21wdCIsInN1Z2dlc3RlZFRpdGxlIiwic2V0U3VnZ2VzdGVkVGl0bGUiLCJpc1RpdGxlQWxlcnRWaXNpYmxlIiwic2V0SXNUaXRsZUFsZXJ0VmlzaWJsZSIsImVkaXRvclJlZiIsImhhbmRsZVNlbGVjdGlvbkNoYW5nZSIsIkFycmF5IiwiaXNBcnJheSIsImhhbmRsZVNhdmUiLCJpZCIsImhhbmRsZURlbGV0ZSIsImhhbmRsZUFpQWN0aW9uIiwiYWN0aW9uIiwicGF5bG9hZCIsInJlc3VsdCIsImNvbnNvbGUiLCJsb2ciLCJjaHVuayIsInByZXYiLCJjYXRjaCIsImVycm9yIiwiZGVzY3JpcHRpb24iLCJFcnJvciIsIm1lc3NhZ2UiLCJ2YXJpYW50IiwidGl0bGVSZXN1bHQiLCJ0cmltIiwibmV3VGFncyIsInVuaXF1ZVRhZ3MiLCJTZXQiLCJoYW5kbGVJbnNlcnRSZXN1bHQiLCJoYW5kbGVSZXBsYWNlUmVzdWx0IiwicmVwbGFjZSIsImRpdiIsImNsYXNzTmFtZSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJoMiIsImFzQ2hpbGQiLCJzaXplIiwiZGlzYWJsZWQiLCJvbkNsaWNrIiwibWFwIiwidGFnIiwicmVmIiwicHJldmlldyIsImhpZGVUb29sYmFyIiwib25TZWxlY3Rpb25DaGFuZ2UiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwic291cmNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NoteEditor.tsx\n"));

/***/ })

});