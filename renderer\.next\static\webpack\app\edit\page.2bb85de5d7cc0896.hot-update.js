"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                        const CherryMarkdown = CherryModule.default || CherryModule;\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient || !CherryClass || !containerRef.current) return;\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%'\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 添加自定義樣式\n                                const existingStyle = container.querySelector('style');\n                                if (existingStyle) {\n                                    existingStyle.remove();\n                                }\n                                const style = document.createElement('style');\n                                style.textContent = \"\\n                .cherry-markdown-editor .cherry {\\n                  border: 1px solid hsl(var(--border)) !important;\\n                  border-radius: 6px !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer {\\n                  font-size: 14px !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h1 {\\n                  font-size: 1.5em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h2 {\\n                  font-size: 1.3em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h3 {\\n                  font-size: 1.2em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h4 {\\n                  font-size: 1.1em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h5 {\\n                  font-size: 1em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer h6 {\\n                  font-size: 0.9em !important;\\n                }\\n                .cherry-markdown-editor .cherry-previewer p {\\n                  font-size: 14px !important;\\n                  line-height: 1.6 !important;\\n                }\\n                .cherry-markdown-editor .cherry-editor {\\n                  font-size: 14px !important;\\n                }\\n                .cherry-markdown-editor .cherry-editor,\\n                .cherry-markdown-editor .cherry-previewer {\\n                  overflow-y: auto !important;\\n                }\\n              \";\n                                container.appendChild(style);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            try {\n                cherryRef.current = new CherryClass(cherryConfig);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息\n    if (!isClient || !CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 246,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 234,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 252,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});