{"name": "renderer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@milkdown/core": "^7.15.5", "@milkdown/preset-commonmark": "^7.15.5", "@milkdown/react": "^7.15.5", "@milkdown/theme-nord": "^7.15.5", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@uiw/react-md-editor": "^4.0.8", "cherry-markdown": "^0.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "echarts": "^6.0.0", "lucide-react": "^0.542.0", "next": "15.5.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-resizable-panels": "^3.0.5", "rehype-rewrite": "^4.0.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}