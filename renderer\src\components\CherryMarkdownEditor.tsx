"use client";

import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from "react";
import { useTheme } from "next-themes";
import dynamic from "next/dynamic";

// Fallback to @uiw/react-md-editor if <PERSON> fails
const MDEditor = dynamic(
  () => import("@uiw/react-md-editor").then((mod) => mod.default),
  { ssr: false }
);

interface CherryMarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  preview?: "edit" | "preview" | "live";
  hideToolbar?: boolean;
  className?: string;
  onSelectionChange?: (selectedText: string) => void;
}

export interface CherryMarkdownEditorRef {
  getMarkdown: () => string;
  setMarkdown: (value: string) => void;
  getSelection: () => string;
  focus: () => void;
}

const CherryMarkdownEditor = forwardRef<CherryMarkdownEditorRef, CherryMarkdownEditorProps>(
  ({ value, onChange, preview = "live", hideToolbar = false, className = "", onSelectionChange }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const cherryRef = useRef<any>(null);
    const [isClient, setIsClient] = useState(false);
    const [CherryClass, setCherryClass] = useState<any>(null);
    const [cherryLoadFailed, setCherryLoadFailed] = useState(false);
    const { theme } = useTheme();

    // 確保只在客戶端運行
    useEffect(() => {
      setIsClient(true);

      // 設置超時，如果 Cherry 載入時間過長則使用備用編輯器
      const timeout = setTimeout(() => {
        console.log("Cherry loading timeout, using fallback editor");
        setCherryLoadFailed(true);
      }, 5000); // 5秒超時

      // 動態導入 Cherry Markdown
      const loadCherry = async () => {
        try {
          console.log("Starting to load Cherry Markdown...");

          // Try different import methods
          let CherryMarkdown;

          try {
            // First try the core version
            const CherryModule = await import("cherry-markdown/dist/cherry-markdown.core");
            CherryMarkdown = CherryModule.default || CherryModule;
            console.log("Cherry core module loaded:", CherryModule);
          } catch (coreError) {
            console.log("Core version failed, trying full version:", coreError);
            // Fallback to full version
            const CherryModule = await import("cherry-markdown");
            CherryMarkdown = CherryModule.default || CherryModule;
            console.log("Cherry full module loaded:", CherryModule);
          }

          console.log("Cherry constructor:", CherryMarkdown);
          console.log("Cherry constructor type:", typeof CherryMarkdown);

          // CSS 需要在全域載入，不用動態導入
          if (typeof CherryMarkdown === 'function') {
            console.log("Setting Cherry class...");
            // 使用函數式更新，避免 React 嘗試執行 Class
            setCherryClass(() => CherryMarkdown);
            console.log("Cherry class set successfully");
            clearTimeout(timeout); // 成功載入，清除超時
          } else {
            console.error("Failed to load Cherry Markdown: not a constructor", CherryMarkdown);
            setCherryLoadFailed(true);
          }
        } catch (error) {
          console.error("Failed to load Cherry Markdown. Raw error object:", error);
          if (error instanceof Error) {
            console.error("Error name:", error.name);
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
          } else {
            console.error("The thrown object was not an Error instance. It is:", JSON.stringify(error, null, 2));
          }
          // Set failed state to use fallback editor
          setCherryLoadFailed(true);
          clearTimeout(timeout);
        }
      };

      loadCherry();

      return () => {
        clearTimeout(timeout);
      };
    }, []);

    useImperativeHandle(ref, () => ({
      getMarkdown: () => cherryRef.current?.getMarkdown() || "",
      setMarkdown: (value: string) => {
        if (cherryRef.current) {
          cherryRef.current.setMarkdown(value);
        }
      },
      getSelection: () => {
        if (typeof window === "undefined") return "";
        const selection = window.getSelection();
        return selection ? selection.toString() : "";
      },
      focus: () => {
        if (containerRef.current) {
          const editor = containerRef.current.querySelector('.CodeMirror');
          if (editor) {
            (editor as any).CodeMirror?.focus();
          }
        }
      },
    }));

    useEffect(() => {
      console.log("Cherry initialization effect triggered", {
        isClient,
        CherryClass: !!CherryClass,
        containerRef: !!containerRef.current,
        preview,
        hideToolbar,
        theme
      });

      if (!isClient || !CherryClass || !containerRef.current) {
        console.log("Cherry initialization skipped - missing requirements");
        return;
      }

      console.log("Starting Cherry initialization...");

      // 銷毀現有實例
      if (cherryRef.current) {
        console.log("Destroying existing Cherry instance");
        cherryRef.current.destroy?.();
        cherryRef.current = null;
      }

      // 清空容器
      containerRef.current.innerHTML = '';
      console.log("Container cleared");

      // 基本配置
      const cherryConfig = {
        id: containerRef.current,
        value: value,
        editor: {
          defaultModel: preview === 'preview' ? 'previewOnly' :
                       preview === 'edit' ? 'editOnly' : 'edit&preview',
          height: '100%',
          autoHeight: false,
          codemirror: {
            lineNumbers: true,
            lineWrapping: true,
            theme: theme === 'dark' ? 'material-darker' : 'default',
          }
        },
        previewer: {
          dom: false,
          className: 'cherry-previewer',
          enablePreviewerBubble: false,
        },
        toolbars: hideToolbar ? {
          toolbar: false,
          bubble: false,
          float: false,
          sidebar: false,
        } : {
          toolbar: [
            'bold', 'italic', 'strikethrough', '|',
            'header', 'list', 'quote', 'hr', '|',
            'link', 'image', 'code', 'table', '|',
            'undo', 'redo'
          ],
        },
        callback: {
          afterChange: (markdown: string) => {
            if (onChange) {
              onChange(markdown);
            }
          },
          afterInit: () => {
            console.log("Cherry afterInit callback triggered");
            // 設置樣式
            const container = containerRef.current;
            if (container) {
              container.setAttribute('data-color-mode', theme === "dark" ? 'dark' : 'light');

              // 確保編輯器高度正確並且不會溢出
              const cherryInstance = cherryRef.current;
              if (cherryInstance) {
                // 強制設置容器樣式
                const cherryElement = container.querySelector('.cherry') as HTMLElement;
                if (cherryElement) {
                  cherryElement.style.position = 'relative';
                  cherryElement.style.height = '100%';
                  cherryElement.style.maxHeight = '100%';
                  cherryElement.style.overflow = 'hidden';
                }

                // 刷新編輯器
                if (cherryInstance.editor) {
                  setTimeout(() => {
                    cherryInstance.editor.refresh();
                  }, 100);
                }
              }
            }
          },
        },
      };

      console.log("Cherry config prepared:", cherryConfig);

      try {
        console.log("Creating new Cherry instance...");
        cherryRef.current = new CherryClass(cherryConfig);
        console.log("Cherry instance created successfully:", cherryRef.current);
      } catch (error) {
        console.error('Failed to initialize Cherry Markdown:', error);
      }

      return () => {
        if (cherryRef.current) {
          cherryRef.current.destroy?.();
          cherryRef.current = null;
        }
      };
    }, [isClient, CherryClass, hideToolbar, preview, theme]);

    // 當 value 從外部更新時，同步到編輯器
    useEffect(() => {
      if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {
        cherryRef.current.setMarkdown(value);
      }
    }, [value]);

    // 處理選擇變更
    useEffect(() => {
      if (!isClient) return;

      const handleSelection = () => {
        const selection = window.getSelection();
        const selectedText = selection ? selection.toString() : "";
        
        // 檢查選取的文字是否在編輯器內部
        if (selection?.anchorNode && containerRef.current?.contains(selection.anchorNode)) {
          if (onSelectionChange) {
            onSelectionChange(selectedText);
          }
        } else if (onSelectionChange) {
          onSelectionChange("");
        }
      };

      document.addEventListener("mouseup", handleSelection);
      document.addEventListener("keyup", handleSelection);
      document.addEventListener("selectionchange", handleSelection);

      return () => {
        document.removeEventListener("mouseup", handleSelection);
        document.removeEventListener("keyup", handleSelection);
        document.removeEventListener("selectionchange", handleSelection);
      };
    }, [isClient, onSelectionChange]);

    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器
    if (!isClient) {
      return (
        <div
          className={`cherry-markdown-editor ${className}`}
          style={{
            height: "100%",
            border: "1px solid hsl(var(--border))",
            borderRadius: "6px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "hsl(var(--background))"
          }}
        >
          <div>載入編輯器中...</div>
        </div>
      );
    }

    // 如果 Cherry 載入失敗，使用 MDEditor 作為備用編輯器
    if (!CherryClass) {
      if (cherryLoadFailed) {
        return (
          <div className={`cherry-markdown-editor ${className}`} style={{ height: "100%" }}>
            <MDEditor
              value={value}
              onChange={(val) => onChange && onChange(val || "")}
              preview={preview === "preview" ? "preview" : preview === "edit" ? "edit" : "live"}
              hideToolbar={hideToolbar}
              data-color-mode={theme === "dark" ? "dark" : "light"}
              style={{ height: "100%" }}
            />
          </div>
        );
      }

      return (
        <div
          className={`cherry-markdown-editor ${className}`}
          style={{
            height: "100%",
            border: "1px solid hsl(var(--border))",
            borderRadius: "6px",
            display: "flex",
            flexDirection: "column",
            backgroundColor: "hsl(var(--background))"
          }}
        >
          <div style={{ padding: "8px", borderBottom: "1px solid hsl(var(--border))", fontSize: "12px", color: "hsl(var(--muted-foreground))" }}>
            載入編輯器中...
          </div>
          <div style={{ flex: 1, display: "flex", alignItems: "center", justifyContent: "center" }}>
            <div>正在載入 Cherry Markdown 編輯器...</div>
          </div>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className={`cherry-markdown-editor ${className}`}
        style={{
          height: "100%",
          width: "100%",
          minHeight: "400px",
          maxHeight: "100%",
          display: "flex",
          flexDirection: "column",
          position: "relative",
          overflow: "hidden",
          contain: "layout style"
        }}
        data-color-mode={theme === "dark" ? "dark" : "light"}
      />
    );
  }
);

CherryMarkdownEditor.displayName = "CherryMarkdownEditor";

export default CherryMarkdownEditor;