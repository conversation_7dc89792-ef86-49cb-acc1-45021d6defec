"use client";

import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from "react";
import { useTheme } from "next-themes";
import dynamic from "next/dynamic";

interface CherryMarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  preview?: "edit" | "preview" | "live";
  hideToolbar?: boolean;
  className?: string;
  onSelectionChange?: (selectedText: string) => void;
}

export interface CherryMarkdownEditorRef {
  getMarkdown: () => string;
  setMarkdown: (value: string) => void;
  getSelection: () => string;
  focus: () => void;
}

const CherryMarkdownEditor = forwardRef<CherryMarkdownEditorRef, CherryMarkdownEditorProps>(
  ({ value, onChange, preview = "live", hideToolbar = false, className = "", onSelectionChange }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const cherryRef = useRef<any>(null);
    const [isClient, setIsClient] = useState(false);
    const [CherryClass, setCherryClass] = useState<any>(null);
    const { theme } = useTheme();

    // 確保只在客戶端運行
    useEffect(() => {
      setIsClient(true);
      
      // 動態導入 Cherry Markdown
      const loadCherry = async () => {
        try {
          const CherryModule = await import("cherry-markdown");
          const CherryMarkdown = CherryModule.default || CherryModule;
          // CSS 需要在全域載入，不用動態導入
          if (typeof CherryMarkdown === 'function') {
            // 使用函數式更新，避免 React 嘗試執行 Class
            setCherryClass(() => CherryMarkdown);
          } else {
            console.error("Failed to load Cherry Markdown: not a constructor", CherryMarkdown);
          }
        } catch (error) {
          console.error("Failed to load Cherry Markdown. Raw error object:", error);
          if (error instanceof Error) {
            console.error("Error name:", error.name);
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
          } else {
            console.error("The thrown object was not an Error instance. It is:", JSON.stringify(error, null, 2));
          }
        }
      };
      
      loadCherry();
    }, []);

    useImperativeHandle(ref, () => ({
      getMarkdown: () => cherryRef.current?.getMarkdown() || "",
      setMarkdown: (value: string) => {
        if (cherryRef.current) {
          cherryRef.current.setMarkdown(value);
        }
      },
      getSelection: () => {
        if (typeof window === "undefined") return "";
        const selection = window.getSelection();
        return selection ? selection.toString() : "";
      },
      focus: () => {
        if (containerRef.current) {
          const editor = containerRef.current.querySelector('.CodeMirror');
          if (editor) {
            (editor as any).CodeMirror?.focus();
          }
        }
      },
    }));

    useEffect(() => {
      if (!isClient || !CherryClass || !containerRef.current) return;

      // 銷毀現有實例
      if (cherryRef.current) {
        cherryRef.current.destroy?.();
        cherryRef.current = null;
      }

      // 清空容器
      containerRef.current.innerHTML = '';

      // 基本配置
      const cherryConfig = {
        id: containerRef.current,
        value: value,
        editor: {
          defaultModel: preview === 'preview' ? 'previewOnly' :
                       preview === 'edit' ? 'editOnly' : 'edit&preview',
          height: '100%',
          autoHeight: false,
          codemirror: {
            lineNumbers: true,
            lineWrapping: true,
            theme: theme === 'dark' ? 'material-darker' : 'default',
          }
        },
        previewer: {
          dom: false,
          className: 'cherry-previewer',
          enablePreviewerBubble: false,
        },
        toolbars: hideToolbar ? {
          toolbar: false,
          bubble: false,
          float: false,
          sidebar: false,
        } : {
          toolbar: [
            'bold', 'italic', 'strikethrough', '|',
            'header', 'list', 'quote', 'hr', '|',
            'link', 'image', 'code', 'table', '|',
            'undo', 'redo'
          ],
        },
        callback: {
          afterChange: (markdown: string) => {
            if (onChange) {
              onChange(markdown);
            }
          },
          afterInit: () => {
            // 設置樣式
            const container = containerRef.current;
            if (container) {
              container.setAttribute('data-color-mode', theme === "dark" ? 'dark' : 'light');

              // 確保編輯器高度正確並且不會溢出
              const cherryInstance = cherryRef.current;
              if (cherryInstance) {
                // 強制設置容器樣式
                const cherryElement = container.querySelector('.cherry') as HTMLElement;
                if (cherryElement) {
                  cherryElement.style.position = 'relative';
                  cherryElement.style.height = '100%';
                  cherryElement.style.maxHeight = '100%';
                  cherryElement.style.overflow = 'hidden';
                }

                // 刷新編輯器
                if (cherryInstance.editor) {
                  setTimeout(() => {
                    cherryInstance.editor.refresh();
                  }, 100);
                }
              }
            }
          },
        },
      };

      try {
        cherryRef.current = new CherryClass(cherryConfig);
      } catch (error) {
        console.error('Failed to initialize Cherry Markdown:', error);
      }

      return () => {
        if (cherryRef.current) {
          cherryRef.current.destroy?.();
          cherryRef.current = null;
        }
      };
    }, [isClient, CherryClass, hideToolbar, preview, theme]);

    // 當 value 從外部更新時，同步到編輯器
    useEffect(() => {
      if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {
        cherryRef.current.setMarkdown(value);
      }
    }, [value]);

    // 處理選擇變更
    useEffect(() => {
      if (!isClient) return;

      const handleSelection = () => {
        const selection = window.getSelection();
        const selectedText = selection ? selection.toString() : "";
        
        // 檢查選取的文字是否在編輯器內部
        if (selection?.anchorNode && containerRef.current?.contains(selection.anchorNode)) {
          if (onSelectionChange) {
            onSelectionChange(selectedText);
          }
        } else if (onSelectionChange) {
          onSelectionChange("");
        }
      };

      document.addEventListener("mouseup", handleSelection);
      document.addEventListener("keyup", handleSelection);
      document.addEventListener("selectionchange", handleSelection);

      return () => {
        document.removeEventListener("mouseup", handleSelection);
        document.removeEventListener("keyup", handleSelection);
        document.removeEventListener("selectionchange", handleSelection);
      };
    }, [isClient, onSelectionChange]);

    // 如果在服務端或還未載入，顯示載入訊息
    if (!isClient || !CherryClass) {
      return (
        <div 
          className={`cherry-markdown-editor ${className}`}
          style={{ 
            height: "100%",
            border: "1px solid hsl(var(--border))",
            borderRadius: "6px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "hsl(var(--background))"
          }}
        >
          <div>載入編輯器中...</div>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className={`cherry-markdown-editor ${className}`}
        style={{
          height: "100%",
          width: "100%",
          minHeight: "400px",
          maxHeight: "100%",
          display: "flex",
          flexDirection: "column",
          position: "relative",
          overflow: "hidden",
          contain: "layout style"
        }}
        data-color-mode={theme === "dark" ? "dark" : "light"}
      />
    );
  }
);

CherryMarkdownEditor.displayName = "CherryMarkdownEditor";

export default CherryMarkdownEditor;