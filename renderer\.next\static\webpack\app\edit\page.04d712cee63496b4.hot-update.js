"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                        const CherryMarkdown = CherryModule.default || CherryModule;\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient || !CherryClass || !containerRef.current) return;\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%'\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                            // 自定義樣式已移至 globals.css\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            try {\n                cherryRef.current = new CherryClass(cherryConfig);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息\n    if (!isClient || !CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 201,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 189,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 207,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});