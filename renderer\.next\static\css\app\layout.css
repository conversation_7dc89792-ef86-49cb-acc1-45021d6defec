/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[3]!./node_modules/cherry-markdown/dist/cherry-markdown.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/* BASICS */
.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */
.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}

.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */
.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}

.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker {
  color: black;
}

.CodeMirror-guttermarker-subtle {
  color: #999;
}

/* CURSOR */
.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}

/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}

.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}

.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}

.cm-fat-cursor-mark {
  background-color: rgba(20, 255, 20, 0.5);
  animation: blink 1.06s steps(1) infinite;
}

.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@keyframes blink {
  50% {
    background-color: transparent;
  }
}
/* Can style cursor different in overwrite (non-insert) mode */
.cm-tab {
  display: inline-block;
  text-decoration: inherit;
}

.CodeMirror-rulers {
  position: absolute;
  left: 0;
  right: 0;
  top: -50px;
  bottom: 0;
  overflow: hidden;
}

.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0;
  bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */
.cm-s-default .cm-header {
  color: blue;
}

.cm-s-default .cm-quote {
  color: #090;
}

.cm-negative {
  color: #d44;
}

.cm-positive {
  color: #292;
}

.cm-header, .cm-strong {
  font-weight: bold;
}

.cm-em {
  font-style: italic;
}

.cm-link {
  text-decoration: underline;
}

.cm-strikethrough {
  text-decoration: line-through;
}

.cm-s-default .cm-keyword {
  color: #708;
}

.cm-s-default .cm-atom {
  color: #219;
}

.cm-s-default .cm-number {
  color: #164;
}

.cm-s-default .cm-def {
  color: #00f;
}

.cm-s-default .cm-variable-2 {
  color: #05a;
}

.cm-s-default .cm-variable-3, .cm-s-default .cm-type {
  color: #085;
}

.cm-s-default .cm-comment {
  color: #a50;
}

.cm-s-default .cm-string {
  color: #a11;
}

.cm-s-default .cm-string-2 {
  color: #f50;
}

.cm-s-default .cm-meta {
  color: #555;
}

.cm-s-default .cm-qualifier {
  color: #555;
}

.cm-s-default .cm-builtin {
  color: #30a;
}

.cm-s-default .cm-bracket {
  color: #997;
}

.cm-s-default .cm-tag {
  color: #170;
}

.cm-s-default .cm-attribute {
  color: #00c;
}

.cm-s-default .cm-hr {
  color: #999;
}

.cm-s-default .cm-link {
  color: #00c;
}

.cm-s-default .cm-error {
  color: #f00;
}

.cm-invalidchar {
  color: #f00;
}

.CodeMirror-composing {
  border-bottom: 2px solid;
}

/* Default styles for common addons */
div.CodeMirror span.CodeMirror-matchingbracket {
  color: #0b0;
}

div.CodeMirror span.CodeMirror-nonmatchingbracket {
  color: #a22;
}

.CodeMirror-matchingtag {
  background: rgba(255, 150, 0, 0.3);
}

.CodeMirror-activeline-background {
  background: #e8f2ff;
}

/* STOP */
/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */
.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important; /* Things will break if this is overridden */
  /* 50px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -50px;
  margin-right: -50px;
  padding-bottom: 50px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
}

.CodeMirror-sizer {
  position: relative;
  border-right: 50px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
  outline: none;
}

.CodeMirror-vscrollbar {
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}

.CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}

.CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}

.CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}

.CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}

.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -50px;
}

.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}

.CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}

.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}

.CodeMirror-gutter-wrapper ::-moz-selection {
  background-color: transparent;
}

.CodeMirror-gutter-wrapper ::selection {
  background-color: transparent;
}

.CodeMirror-gutter-wrapper ::-moz-selection {
  background-color: transparent;
}

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}

.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  /* Reset some styles that the rest of the page might have set */
  border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  font-variant-ligatures: contextual;
}

.CodeMirror-wrap pre.CodeMirror-line,
.CodeMirror-wrap pre.CodeMirror-line-like {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px; /* Force widget margins to stay inside of the container */
}

.CodeMirror-rtl pre {
  direction: rtl;
}

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}

.CodeMirror-measure pre {
  position: static;
}

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}

div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected {
  background: #d9d9d9;
}

.CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}

.CodeMirror-crosshair {
  cursor: crosshair;
}

.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}

.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection {
  background: #d7d4f0;
}

.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, 0.4);
}

/* Used to force a border model for a node */
.cm-force-border {
  padding-right: 0.1px;
}

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}
/* See issue #2901 */
.cm-tab-wrap-hack:after {
  content: "";
}

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext {
  background: none;
}

:root {
  /* ========== 尺寸系统 ========== */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 48px;
  --radius-none: 0;
  --radius-sm: 2px;
  --radius-md: 3px;
  --radius-lg: 6px;
  --radius-xl: 8px;
  --radius-2xl: 12px;
  --radius-full: 9999px;
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 0 10px rgba(128, 145, 165, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  /* ========== 字体系统 ========== */
  --font-family-sans: "Helvetica Neue", Arial, "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  --font-family-mono: "Menlo", "Liberation Mono", "Consolas", "DejaVu Sans Mono", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  /* ========== 边框系统 ========== */
  --border-width-0: 0px;
  --border-width-1: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;
  /* ========== 高度尺寸 ========== */
  --height-toolbar: 48px;
  --height-button: 38px;
  /* ========== 透明度 ========== */
  --opacity-0: 0;
  --opacity-25: 0.25;
  --opacity-50: 0.5;
  --opacity-75: 0.75;
  --opacity-90: 0.9;
  --opacity-95: 0.95;
  --opacity-100: 1;
  /* ========== 毛玻璃效果 ========== */
  --blur-none: 0;
  --blur-sm: 4px;
  --blur-md: 8px;
  --blur-lg: 12px;
  --blur-xl: 16px;
  --blur-2xl: 24px;
  --blur-3xl: 40px;
  --glass-bg-white: rgba(255, 255, 255, 0.8);
  --glass-bg-gray: rgba(248, 250, 252, 0.8);
  --glass-bg-dark: rgba(51, 65, 85, 0.8);
  --glass-bg-black: rgba(0, 0, 0, 0.8);
  --glass-border-light: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  /* ========== Z-index 层级 ========== */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

.cherry {
  /* ========== 基础色彩映射 ========== */
  --base-font-color: #3f4a56;
  --base-sub-font-color: #6d6e6f;
  --base-border-color: #dfe6ee;
  --base-editor-bg: #ffffff;
  --base-previewer-bg: #ffffff;
  /* ========== 主题色彩系统 ========== */
  --primary-color: #3582fb;
  --secondary-color: #f0f4ff;
  /* ========== 系统状态颜色 ========== */
  --color-success: var(--oc-green-6);
  --color-warning: var(--oc-yellow-6);
  --color-error: var(--oc-red-6);
  --color-info: var(--oc-blue-6);
  /* ========== 工具栏相关 ========== */
  --toolbar-bg: var(--oc-white);
  --toolbar-border: var(--base-border-color);
  --toolbar-padding: var(--spacing-xs) var(--spacing-xl);
  --toolbar-font-size: var(--font-size-sm);
  --toolbar-radius: none;
  --toolbar-shadow: var(--shadow-md);
  --toolbar-split-color: var(--base-border-color);
  --toolbar-min-height: 48px;
  --toolbar-btn-color: #3f4a56;
  --toolbar-btn-bg: transparent;
  --toolbar-btn-hover-color: #5d9bfc;
  --toolbar-btn-hover-bg: #ebf3ff;
  --toolbar-btn-disabled: #ccc;
  --toolbar-btn-radius: var(--radius-lg);
  --toolbar-btn-padding: 0 var(--spacing-md);
  --toolbar-btn-height: var(--height-button);
  /* ========== 工具栏的下拉菜单 ========== */
  --dropdown-bg: var(--toolbar-bg);
  --dropdown-border: var(--base-border-color);
  --dropdown-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --dropdown-radius: var(--radius-xl);
  --dropdown-padding: var(--spacing-xs) 0;
  --dropdown-item-height: var(--height-button);
  --dropdown-item-radius: var(--dropdown-radius);
  --dropdown-item-padding: var(--spacing-sm) var(--spacing-md);
  --dropdown-item-hover-bg: var(--oc-gray-2);
  --dropdown-item-color: var(--toolbar-btn-color);
  --dropdown-item-hover-color: var(--base-font-color);
  --dropdown-item-active-bg: var(--secondary-color);
  --dropdown-item-active-color: var(--primary-color);
  /* ========== 编辑气泡 ========== */
  --bubble-bg: var(--toolbar-bg);
  --bubble-border: var(--base-border-color);
  --bubble-shadow: var(--shadow-md);
  --bubble-radius: var(--radius-xl);
  --bubble-padding: var(--toolbar-btn-padding);
  --bubble-btn-height: var(--height-button);
  --bubble-z-index: var(--z-index-popover);
  /* ========== 编辑器相关 ========== */
  --editor-header-color: var(--base-font-color);
  --editor-comment-color: var(--oc-blue-6);
  --editor-quote-color: var(--oc-blue-6);
  --editor-string-color: var(--base-font-color);
  --editor-link-color: var(--oc-blue-6);
  --editor-url-bg-color: #d7e6fe;
  --editor-v2-color: var(--primary-color);
  --editor-v3-color: var(--secondary-color);
  --editor-keyword-color: var(--base-font-color);
  --editor-cursor-color: var(--primary-color);
  --editor-selection-bg: var(--oc-blue-1);
  --editor-line-number-color: var(--oc-gray-4);
  --editor-active-line-bg: var(--oc-gray-0);
  /* ========== 预览器相关 ========== */
  --previewer-mobile-bg: var(--base-previewer-bg);
  /* ========== Markdown 元素样式 ========== */
  --md-heading-color: var(--base-font-color);
  --md-paragraph-color: var(--base-font-color);
  --md-paragraph-line-height: var(--line-height-relaxed);
  --md-link-color: var(--oc-blue-6);
  --md-link-hover-color: var(--color-link-hover);
  --md-inline-code-color: var(--color-error);
  --md-inline-code-bg: #e5e5e5;
  --md-blockquote-bg: rgba(102, 128, 153, 0.05);
  --md-blockquote-border: #D6DBDF;
  --md-blockquote-color: var(--base-sub-font-color);
  --md-table-border: var(--base-border-color);
  --md-hr-border: var(--base-border-color);
  --md-toc-bg: var(--oc-gray-0);
  --md-toc-border-color: var(--base-border-color);
  --md-toc-radius: var(--radius-lg);
  --md-toc-padding: var(--spacing-lg);
  --md-toc-title-color: var(--md-heading-color);
  --md-toc-link-hover-bg: var(--oc-gray-1);
  --md-toc-link-active-bg: var(--oc-gray-2);
  --md-toc-link-radius: var(--radius-md);
  --md-toc-link-color: var(--md-paragraph-color);
  --md-toc-link-hover-color: var(--md-link-hover-color);
  --md-toc-indicator-width: var(--border-width-4);
  --md-toc-indicator-color: var(--oc-gray-3);
  --md-toc-indicator-hover-color: var(--md-toc-link-hover-color);
  --md-toc-indicator-gap: var(--spacing-lg);
  /* ========== 手风琴组件样式 ========== */
  --accordion-bg: var(--base-previewer-bg);
  --accordion-border: var(--base-border-color);
  --accordion-radius: var(--radius-lg);
  --accordion-shadow: var(--shadow-md);
  --accordion-summary-bg: var(--primary-color);
  --accordion-summary-color: var(--oc-white);
  --accordion-summary-hover-bg: var(--color-primary-hover);
  --accordion-body-bg: var(--base-previewer-bg);
  --accordion-body-color: var(--base-font-color);
  --accordion-body-border: var(--base-border-color);
  /* ========== 其余单个组件 ========== */
  --drag-border-color: #ebedee;
}

/*
 * Open Color
 * https://github.com/yeun/open-color
 *
 * Copyright (c) 2016 heeyeun
 * Licensed under the MIT License
 *
 *  𝗖 𝗢 𝗟 𝗢 𝗥
 *  v 1.9.1
 *
 *  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ */
:root {
  /*  General
   *  ─────────────────────────────────── */
  --oc-white: #ffffff;
  --oc-black: #000000;
  /*  Gray
   *  ─────────────────────────────────── */
  --oc-gray-0: #f8f9fa;
  --oc-gray-1: #f1f3f5;
  --oc-gray-2: #e9ecef;
  --oc-gray-3: #dee2e6;
  --oc-gray-4: #ced4da;
  --oc-gray-5: #adb5bd;
  --oc-gray-6: #868e96;
  --oc-gray-7: #495057;
  --oc-gray-8: #343a40;
  --oc-gray-9: #212529;
  /*  Red
   *  ─────────────────────────────────── */
  --oc-red-0: #fff5f5;
  --oc-red-1: #ffe3e3;
  --oc-red-2: #ffc9c9;
  --oc-red-3: #ffa8a8;
  --oc-red-4: #ff8787;
  --oc-red-5: #ff6b6b;
  --oc-red-6: #fa5252;
  --oc-red-7: #f03e3e;
  --oc-red-8: #e03131;
  --oc-red-9: #c92a2a;
  /*  Pink
   *  ─────────────────────────────────── */
  --oc-pink-0: #fff0f6;
  --oc-pink-1: #ffdeeb;
  --oc-pink-2: #fcc2d7;
  --oc-pink-3: #faa2c1;
  --oc-pink-4: #f783ac;
  --oc-pink-5: #f06595;
  --oc-pink-6: #e64980;
  --oc-pink-7: #d6336c;
  --oc-pink-8: #c2255c;
  --oc-pink-9: #a61e4d;
  /*  Grape
   *  ─────────────────────────────────── */
  --oc-grape-0: #f8f0fc;
  --oc-grape-1: #f3d9fa;
  --oc-grape-2: #eebefa;
  --oc-grape-3: #e599f7;
  --oc-grape-4: #da77f2;
  --oc-grape-5: #cc5de8;
  --oc-grape-6: #be4bdb;
  --oc-grape-7: #ae3ec9;
  --oc-grape-8: #9c36b5;
  --oc-grape-9: #862e9c;
  /*  Violet
   *  ─────────────────────────────────── */
  --oc-violet-0: #f3f0ff;
  --oc-violet-1: #e5dbff;
  --oc-violet-2: #d0bfff;
  --oc-violet-3: #b197fc;
  --oc-violet-4: #9775fa;
  --oc-violet-5: #845ef7;
  --oc-violet-6: #7950f2;
  --oc-violet-7: #7048e8;
  --oc-violet-8: #6741d9;
  --oc-violet-9: #5f3dc4;
  /*  Indigo
   *  ─────────────────────────────────── */
  --oc-indigo-0: #edf2ff;
  --oc-indigo-1: #dbe4ff;
  --oc-indigo-2: #bac8ff;
  --oc-indigo-3: #91a7ff;
  --oc-indigo-4: #748ffc;
  --oc-indigo-5: #5c7cfa;
  --oc-indigo-6: #4c6ef5;
  --oc-indigo-7: #4263eb;
  --oc-indigo-8: #3b5bdb;
  --oc-indigo-9: #364fc7;
  /*  Blue
   *  ─────────────────────────────────── */
  --oc-blue-0: #e7f5ff;
  --oc-blue-1: #d0ebff;
  --oc-blue-2: #a5d8ff;
  --oc-blue-3: #74c0fc;
  --oc-blue-4: #4dabf7;
  --oc-blue-5: #339af0;
  --oc-blue-6: #228be6;
  --oc-blue-7: #1c7ed6;
  --oc-blue-8: #1971c2;
  --oc-blue-9: #1864ab;
  /*  Cyan
   *  ─────────────────────────────────── */
  --oc-cyan-0: #e3fafc;
  --oc-cyan-1: #c5f6fa;
  --oc-cyan-2: #99e9f2;
  --oc-cyan-3: #66d9e8;
  --oc-cyan-4: #3bc9db;
  --oc-cyan-5: #22b8cf;
  --oc-cyan-6: #15aabf;
  --oc-cyan-7: #1098ad;
  --oc-cyan-8: #0c8599;
  --oc-cyan-9: #0b7285;
  /*  Teal
   *  ─────────────────────────────────── */
  --oc-teal-0: #e6fcf5;
  --oc-teal-1: #c3fae8;
  --oc-teal-2: #96f2d7;
  --oc-teal-3: #63e6be;
  --oc-teal-4: #38d9a9;
  --oc-teal-5: #20c997;
  --oc-teal-6: #12b886;
  --oc-teal-7: #0ca678;
  --oc-teal-8: #099268;
  --oc-teal-9: #087f5b;
  /*  Green
   *  ─────────────────────────────────── */
  --oc-green-0: #ebfbee;
  --oc-green-1: #d3f9d8;
  --oc-green-2: #b2f2bb;
  --oc-green-3: #8ce99a;
  --oc-green-4: #69db7c;
  --oc-green-5: #51cf66;
  --oc-green-6: #40c057;
  --oc-green-7: #37b24d;
  --oc-green-8: #2f9e44;
  --oc-green-9: #2b8a3e;
  /*  Lime
   *  ─────────────────────────────────── */
  --oc-lime-0: #f4fce3;
  --oc-lime-1: #e9fac8;
  --oc-lime-2: #d8f5a2;
  --oc-lime-3: #c0eb75;
  --oc-lime-4: #a9e34b;
  --oc-lime-5: #94d82d;
  --oc-lime-6: #82c91e;
  --oc-lime-7: #74b816;
  --oc-lime-8: #66a80f;
  --oc-lime-9: #5c940d;
  /*  Yellow
   *  ─────────────────────────────────── */
  --oc-yellow-0: #fff9db;
  --oc-yellow-1: #fff3bf;
  --oc-yellow-2: #ffec99;
  --oc-yellow-3: #ffe066;
  --oc-yellow-4: #ffd43b;
  --oc-yellow-5: #fcc419;
  --oc-yellow-6: #fab005;
  --oc-yellow-7: #f59f00;
  --oc-yellow-8: #f08c00;
  --oc-yellow-9: #e67700;
  /*  Orange
   *  ─────────────────────────────────── */
  --oc-orange-0: #fff4e6;
  --oc-orange-1: #ffe8cc;
  --oc-orange-2: #ffd8a8;
  --oc-orange-3: #ffc078;
  --oc-orange-4: #ffa94d;
  --oc-orange-5: #ff922b;
  --oc-orange-6: #fd7e14;
  --oc-orange-7: #f76707;
  --oc-orange-8: #e8590c;
  --oc-orange-9: #d9480f;
}

.cherry *::-webkit-scrollbar {
  height: 7px;
  width: 7px;
  background: transparent;
}
.cherry *::-webkit-scrollbar:hover {
  background: rgba(128, 128, 128, 0.1);
}
.cherry *::-webkit-scrollbar-thumb {
  background: #d3d7da;
  -webkit-border-radius: 6px;
}
.cherry *::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.6);
}
.cherry *::-webkit-scrollbar-corner {
  background: transparent;
}

@font-face {
  font-family: "ch-icon";
  src: url(/_next/static/media/ch-icon.4a441d79.eot);
  src: url(/_next/static/media/ch-icon.4a441d79.eot?#iefix) format("eot"), url(/_next/static/media/ch-icon.2ff29e1b.woff2) format("woff2"), url(/_next/static/media/ch-icon.dbb1c0e0.woff) format("woff"), url(/_next/static/media/ch-icon.3571e422.ttf) format("truetype"), url(/_next/static/media/ch-icon.4e9cfa55.svg#ch-icon) format("svg");
  font-weight: normal;
  font-style: normal;
}
.ch-icon:before {
  display: inline-block;
  font-family: "ch-icon";
  font-style: normal;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ch-icon-list:before {
  content: "\ea03";
}

.ch-icon-check:before {
  content: "\ea04";
}

.ch-icon-square:before {
  content: "\ea09";
}

.ch-icon-bold:before {
  content: "\ea0a";
}

.ch-icon-code:before {
  content: "\ea0b";
}

.ch-icon-color:before {
  content: "\ea0c";
}

.ch-icon-header:before {
  content: "\ea0d";
}

.ch-icon-image:before {
  content: "\ea0e";
}

.ch-icon-italic:before {
  content: "\ea0f";
}

.ch-icon-link:before {
  content: "\ea10";
}

.ch-icon-ol:before {
  content: "\ea11";
}

.ch-icon-size:before {
  content: "\ea12";
}

.ch-icon-strike:before {
  content: "\ea13";
}

.ch-icon-table:before {
  content: "\ea14";
}

.ch-icon-ul:before {
  content: "\ea15";
}

.ch-icon-underline:before {
  content: "\ea16";
}

.ch-icon-word:before {
  content: "\ea17";
}

.ch-icon-blockquote:before {
  content: "\ea18";
}

.ch-icon-font:before {
  content: "\ea19";
}

.ch-icon-insertClass:before {
  content: "\ea1f";
}

.ch-icon-insertFlow:before {
  content: "\ea20";
}

.ch-icon-insertFormula:before {
  content: "\ea21";
}

.ch-icon-insertGantt:before {
  content: "\ea22";
}

.ch-icon-insertGraph:before {
  content: "\ea23";
}

.ch-icon-insertPie:before {
  content: "\ea24";
}

.ch-icon-insertSeq:before {
  content: "\ea25";
}

.ch-icon-insertState:before {
  content: "\ea26";
}

.ch-icon-line:before {
  content: "\ea27";
}

.ch-icon-preview:before {
  content: "\ea28";
}

.ch-icon-previewClose:before {
  content: "\ea29";
}

.ch-icon-toc:before {
  content: "\ea2a";
}

.ch-icon-sub:before {
  content: "\ea2d";
}

.ch-icon-sup:before {
  content: "\ea2e";
}

.ch-icon-h1:before {
  content: "\ea2f";
}

.ch-icon-h2:before {
  content: "\ea30";
}

.ch-icon-h3:before {
  content: "\ea31";
}

.ch-icon-h4:before {
  content: "\ea32";
}

.ch-icon-h5:before {
  content: "\ea33";
}

.ch-icon-h6:before {
  content: "\ea34";
}

.ch-icon-video:before {
  content: "\ea35";
}

.ch-icon-insert:before {
  content: "\ea36";
}

.ch-icon-little_table:before {
  content: "\ea37";
}

.ch-icon-pdf:before {
  content: "\ea38";
}

.ch-icon-checklist:before {
  content: "\ea39";
}

.ch-icon-close:before {
  content: "\ea40";
}

.ch-icon-fullscreen:before {
  content: "\ea41";
}

.ch-icon-minscreen:before {
  content: "\ea42";
}

.ch-icon-insertChart:before {
  content: "\ea43";
}

.ch-icon-question:before {
  content: "\ea44";
}

.ch-icon-settings:before {
  content: "\ea45";
}

.ch-icon-ok:before {
  content: "\ea46";
}

.ch-icon-br:before {
  content: "\ea47";
}

.ch-icon-normal:before {
  content: "\ea48";
}

.ch-icon-undo:before {
  content: "\ea49";
}

.ch-icon-redo:before {
  content: "\ea50";
}

.ch-icon-copy:before {
  content: "\ea51";
}

.ch-icon-phone:before {
  content: "\ea52";
}

.ch-icon-cherry-table-delete:before {
  content: "\ea53";
}

.ch-icon-cherry-table-insert-bottom:before {
  content: "\ea54";
}

.ch-icon-cherry-table-insert-left:before {
  content: "\ea55";
}

.ch-icon-cherry-table-insert-right:before {
  content: "\ea56";
}

.ch-icon-cherry-table-insert-top:before {
  content: "\ea57";
}

.ch-icon-sort-s:before {
  content: "\ea58";
}

.ch-icon-pinyin:before {
  content: "\ea59";
}

.ch-icon-create:before {
  content: "\ea5a";
}

.ch-icon-download:before {
  content: "\ea5b";
}

.ch-icon-edit:before {
  content: "\ea5c";
}

.ch-icon-export:before {
  content: "\ea5d";
}

.ch-icon-folder-open:before {
  content: "\ea5e";
}

.ch-icon-folder:before {
  content: "\ea5f";
}

.ch-icon-help:before {
  content: "\ea60";
}

.ch-icon-pen-fill:before {
  content: "\ea61";
}

.ch-icon-pen:before {
  content: "\ea62";
}

.ch-icon-tips:before {
  content: "\ea64";
}

.ch-icon-warn:before {
  content: "\ea65";
}

.ch-icon-mistake:before {
  content: "\ea66";
}

.ch-icon-success:before {
  content: "\ea67";
}

.ch-icon-danger:before {
  content: "\ea68";
}

.ch-icon-info:before {
  content: "\ea69";
}

.ch-icon-primary:before {
  content: "\ea6a";
}

.ch-icon-warning:before {
  content: "\ea6b";
}

.ch-icon-justify:before {
  content: "\ea6c";
}

.ch-icon-justifyCenter:before {
  content: "\ea6d";
}

.ch-icon-justifyLeft:before {
  content: "\ea6e";
}

.ch-icon-justifyRight:before {
  content: "\ea6f";
}

.ch-icon-chevronsLeft:before {
  content: "\ea70";
}

.ch-icon-chevronsRight:before {
  content: "\ea71";
}

.ch-icon-trendingUp:before {
  content: "\ea72";
}

.ch-icon-codeBlock:before {
  content: "\ea74";
}

.ch-icon-expand:before {
  content: "\ea75";
}

.ch-icon-unExpand:before {
  content: "\ea76";
}

.ch-icon-swap-vert:before {
  content: "\ea77";
}

.ch-icon-swap:before {
  content: "\ea78";
}

.ch-icon-keyboard:before {
  content: "\ea79";
}

.ch-icon-command:before {
  content: "\ea7a";
}

.ch-icon-search:before {
  content: "\ea7b";
}

.ch-icon-alignCenter:before {
  content: "\ea7c";
}

.ch-icon-alignJustify:before {
  content: "\ea7d";
}

.ch-icon-alignLeft:before {
  content: "\ea7e";
}

.ch-icon-alignRight:before {
  content: "\ea7f";
}

.ch-icon-align:before {
  content: "\ea80";
}

.ch-icon-imgDecoBorder:before {
  content: "\ea81";
}

.ch-icon-imgDecoShadow:before {
  content: "\ea82";
}

.ch-icon-imgDecoRadius:before {
  content: "\ea83";
}

.ch-icon-imgAlignLeft:before {
  content: "\ea84";
}

.ch-icon-imgAlignCenter:before {
  content: "\ea85";
}

.ch-icon-imgAlignRight:before {
  content: "\ea86";
}

.ch-icon-imgAlignFloatLeft:before {
  content: "\ea87";
}

.ch-icon-imgAlignFloatRight:before {
  content: "\ea88";
}

.cherry-markdown {
  word-break: break-all;
  color: var(--md-paragraph-color);
  background-color: var(--base-previewer-bg);
  /* Inline code */
  /* 数学表达式展示 */
}
.cherry-markdown h1,
.cherry-markdown h2,
.cherry-markdown h3,
.cherry-markdown h4,
.cherry-markdown h5,
.cherry-markdown h6,
.cherry-markdown .h1,
.cherry-markdown .h2,
.cherry-markdown .h3,
.cherry-markdown .h4,
.cherry-markdown .h5,
.cherry-markdown .h6 {
  font-family: inherit;
  font-weight: 700;
  line-height: 1.1;
  color: var(--md-heading-color);
}
.cherry-markdown h1 small,
.cherry-markdown h2 small,
.cherry-markdown h3 small,
.cherry-markdown h4 small,
.cherry-markdown h5 small,
.cherry-markdown h6 small,
.cherry-markdown .h1 small,
.cherry-markdown .h2 small,
.cherry-markdown .h3 small,
.cherry-markdown .h4 small,
.cherry-markdown .h5 small,
.cherry-markdown .h6 small,
.cherry-markdown h1 .small,
.cherry-markdown h2 .small,
.cherry-markdown h3 .small,
.cherry-markdown h4 .small,
.cherry-markdown h5 .small,
.cherry-markdown h6 .small,
.cherry-markdown .h1 .small,
.cherry-markdown .h2 .small,
.cherry-markdown .h3 .small,
.cherry-markdown .h4 .small,
.cherry-markdown .h5 .small,
.cherry-markdown .h6 .small {
  font-weight: normal;
  line-height: 1;
  color: var(--oc-gray-5);
}
.cherry-markdown h1,
.cherry-markdown h2,
.cherry-markdown h3 {
  margin-top: 30px;
  margin-bottom: var(--spacing-lg);
}
.cherry-markdown h1 small,
.cherry-markdown h2 small,
.cherry-markdown h3 small,
.cherry-markdown h1 .small,
.cherry-markdown h2 .small,
.cherry-markdown h3 .small {
  font-size: 65%;
}
.cherry-markdown h4,
.cherry-markdown h5,
.cherry-markdown h6 {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}
.cherry-markdown h4 small,
.cherry-markdown h5 small,
.cherry-markdown h6 small,
.cherry-markdown h4 .small,
.cherry-markdown h5 .small,
.cherry-markdown h6 .small {
  font-size: 75%;
}
.cherry-markdown h1,
.cherry-markdown .h1 {
  font-size: 2em;
}
.cherry-markdown h2,
.cherry-markdown .h2 {
  font-size: 1.5em;
}
.cherry-markdown h3,
.cherry-markdown .h3 {
  font-size: 1.25em;
}
.cherry-markdown h4,
.cherry-markdown .h4 {
  font-size: 1em;
}
.cherry-markdown h5,
.cherry-markdown .h5 {
  font-size: 0.875em;
}
.cherry-markdown h6,
.cherry-markdown .h6 {
  font-size: 0.85em;
}
.cherry-markdown b,
.cherry-markdown strong {
  font-weight: bold;
}
.cherry-markdown ul,
.cherry-markdown ol {
  padding-left: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}
.cherry-markdown ul ul,
.cherry-markdown ul ol,
.cherry-markdown ol ul,
.cherry-markdown ol ol {
  margin-bottom: 0;
}
.cherry-markdown ul li,
.cherry-markdown ol li {
  list-style: inherit;
}
.cherry-markdown ul li p,
.cherry-markdown ol li p {
  margin: 0;
}
.cherry-markdown div ul,
.cherry-markdown div ol {
  margin-bottom: 0;
}
.cherry-markdown hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--md-hr-border);
  margin: var(--spacing-lg) 0;
  box-sizing: content-box;
  overflow: visible;
}
.cherry-markdown kbd {
  border: 1px solid var(--base-border-color);
  border-radius: 4px;
  padding: 1px 2px;
  box-sizing: border-box;
  box-shadow: inset 0px -1px var(--base-border-color);
  font-size: 0.85rem;
}
.cherry-markdown table {
  border-collapse: collapse;
  color: var(--md-paragraph-color);
}
.cherry-markdown table th,
.cherry-markdown table td {
  border: 1px solid var(--md-table-border);
  padding: 0.2em 0.4em;
  min-width: 100px;
}
.cherry-markdown table th {
  background-color: var(--md-inline-code-bg);
}
.cherry-markdown .link-quote {
  color: var(--md-link-color);
}
.cherry-markdown a {
  color: var(--md-link-color);
  position: relative;
  text-decoration: none;
}
.cherry-markdown a[target=_blank] {
  padding: 0 2px;
}
.cherry-markdown a[target=_blank]::after {
  content: "\ea10";
  font-size: var(--font-size-xs);
  font-family: "ch-icon";
  margin: 0 2px;
}
.cherry-markdown a:hover {
  text-decoration: underline;
  color: var(--md-link-hover-color);
}
.cherry-markdown em {
  font-style: italic;
}
.cherry-markdown sup {
  vertical-align: super;
}
.cherry-markdown sub {
  vertical-align: sub;
}
.cherry-markdown figure {
  overflow-x: auto;
}
.cherry-markdown p,
.cherry-markdown pre,
.cherry-markdown blockquote,
.cherry-markdown table {
  margin: 0 0 var(--spacing-lg);
}
.cherry-markdown blockquote {
  color: var(--md-paragraph-color);
  padding: 10px 15px;
  border-left: 10px solid var(--md-hr-border);
  background: var(--md-blockquote-bg);
}
.cherry-markdown blockquote p, .cherry-markdown blockquote blockquote, .cherry-markdown blockquote table, .cherry-markdown blockquote pre, .cherry-markdown blockquote ul, .cherry-markdown blockquote ol {
  margin: 0;
}
.cherry-markdown pre {
  padding: var(--spacing-lg);
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
}
.cherry-markdown div[data-type=codeBlock] {
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  border-radius: 2px;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-sm);
  overflow-x: auto;
}
.cherry-markdown div[data-type=codeBlock] > pre {
  margin: 0;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] {
  counter-reset: line;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-].wrap {
  white-space: pre-wrap;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line {
  display: inline-block;
  position: relative;
  padding-left: 3em;
  height: 1.3em;
  line-height: 2em;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line:before {
  counter-increment: line;
  content: counter(line);
  margin-right: 1em;
  position: absolute;
  left: 0;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line:last-child {
  margin-bottom: 0;
}
.cherry-markdown :not(pre) > code {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
  color: var(--md-inline-code-color);
  background-color: var(--md-inline-code-bg);
  border: 1px solid var(--md-hr-border);
}
[data-inline-code-theme=black] .cherry-markdown :not(pre) > code {
  color: var(--base-font-color);
  background-color: var(--md-inline-code-bg);
}
.cherry-markdown a.anchor:before {
  content: "§";
  text-decoration: none;
  width: 15px;
  font-size: 0.5em;
  vertical-align: middle;
  display: inline-block;
  text-align: center;
  margin-left: -15px;
}
.cherry-markdown .toc {
  margin-bottom: var(--spacing-lg);
  padding-left: 0;
  background-color: var(--md-toc-bg);
  border: var(--border-width-1) solid var(--md-toc-border-color);
  border-radius: var(--md-toc-radius);
  padding: var(--md-toc-padding);
}
.cherry-markdown .toc .toc-title {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  color: var(--md-toc-title-color);
}
.cherry-markdown .toc .toc-li {
  list-style: none;
  margin: 0;
  display: flex;
  align-items: center;
  border-left: var(--md-toc-indicator-width) solid var(--md-toc-indicator-color);
  padding-left: var(--md-toc-indicator-gap);
  transition: border-color 50ms ease-out;
}
.cherry-markdown .toc .toc-li a {
  text-decoration: none;
  color: var(--md-toc-link-color);
  flex-grow: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--md-toc-link-radius);
  transition: color 50ms ease-out, background-color 50ms ease-out;
}
.cherry-markdown .toc .toc-li a:hover {
  color: var(--md-toc-link-hover-color);
  background-color: var(--md-toc-link-hover-bg);
}
.cherry-markdown .toc .toc-li a:active {
  background-color: var(--md-toc-link-active-bg);
}
.cherry-markdown .toc .toc-li a.level-1 {
  font-weight: var(--font-weight-semibold);
}
.cherry-markdown .toc .toc-li:hover, .cherry-markdown .toc .toc-li:has(a:active) {
  border-left-color: var(--md-toc-indicator-hover-color);
}
.cherry-markdown .auto-num-toc {
  counter-reset: headtoclevel1;
}
.cherry-markdown .auto-num-toc .toc-li-1 {
  counter-reset: headtoclevel2;
}
.cherry-markdown .auto-num-toc .toc-li-1 a:before {
  counter-increment: headtoclevel1;
  content: counter(headtoclevel1) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-2 {
  counter-reset: headtoclevel3;
}
.cherry-markdown .auto-num-toc .toc-li-2 a:before {
  counter-increment: headtoclevel2;
  content: counter(headtoclevel1) "." counter(headtoclevel2) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-3 {
  counter-reset: headtoclevel4;
}
.cherry-markdown .auto-num-toc .toc-li-3 a:before {
  counter-increment: headtoclevel3;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-4 {
  counter-reset: headtoclevel5;
}
.cherry-markdown .auto-num-toc .toc-li-4 a:before {
  counter-increment: headtoclevel4;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-5 {
  counter-reset: headtoclevel6;
}
.cherry-markdown .auto-num-toc .toc-li-5 a:before {
  counter-increment: headtoclevel5;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) "." counter(headtoclevel5) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-6 a:before {
  counter-increment: headtoclevel6;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) "." counter(headtoclevel5) "." counter(headtoclevel6) ". ";
}
.cherry-markdown .check-list-item {
  list-style: none;
}
.cherry-markdown .check-list-item .ch-icon {
  margin: 0 6px 0 -20px;
}
.cherry-markdown .footnote:not(a) {
  padding-top: 20px;
  border-top: 1px solid var(--md-hr-border);
  margin-top: 50px;
}
.cherry-markdown .footnote:not(a) .footnote-title {
  font-size: 20px;
  margin-top: -38px;
  background-color: var(--md-inline-code-bg);
  width: 60px;
  margin-bottom: var(--spacing-lg);
}
.cherry-markdown .footnote:not(a) .one-footnote {
  color: var(--md-paragraph-color);
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px dotted var(--md-hr-border);
}
.cherry-markdown .footnote:not(a) .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown .cherry-table-container {
  max-width: 100%;
  overflow-x: auto;
}
.cherry-markdown .cherry-table-container .cherry-table th,
.cherry-markdown .cherry-table-container .cherry-table td {
  border: 1px solid var(--md-table-border);
  padding: 0.2em 0.4em;
  min-width: 100px;
}
.cherry-markdown .cherry-table-container .cherry-table th {
  white-space: nowrap;
  background-color: var(--md-inline-code-bg);
}
.cherry-markdown mjx-assistive-mml {
  position: absolute;
  top: 0;
  left: 0;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 1px 0 0 0;
  border: 0;
}
.cherry-markdown.head-num {
  counter-reset: level1;
}
.cherry-markdown.head-num h1 .anchor:before,
.cherry-markdown.head-num h2 .anchor:before,
.cherry-markdown.head-num h3 .anchor:before,
.cherry-markdown.head-num h4 .anchor:before,
.cherry-markdown.head-num h5 .anchor:before,
.cherry-markdown.head-num h6 .anchor:before {
  width: auto;
  font-size: inherit;
  vertical-align: inherit;
  padding-right: 10px;
}
.cherry-markdown.head-num h1 {
  counter-reset: level2;
}
.cherry-markdown.head-num h2 {
  counter-reset: level3;
}
.cherry-markdown.head-num h3 {
  counter-reset: level4;
}
.cherry-markdown.head-num h4 {
  counter-reset: level5;
}
.cherry-markdown.head-num h5 {
  counter-reset: level6;
}
.cherry-markdown.head-num h1 .anchor:before {
  counter-increment: level1;
  content: counter(level1) ". ";
}
.cherry-markdown.head-num h2 .anchor:before {
  counter-increment: level2;
  content: counter(level1) "." counter(level2) " ";
}
.cherry-markdown.head-num h3 .anchor:before {
  counter-increment: level3;
  content: counter(level1) "." counter(level2) "." counter(level3) " ";
}
.cherry-markdown.head-num h4 .anchor:before {
  counter-increment: level4;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) " ";
}
.cherry-markdown.head-num h5 .anchor:before {
  counter-increment: level5;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) "." counter(level5) " ";
}
.cherry-markdown.head-num h6 .anchor:before {
  counter-increment: level6;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) "." counter(level5) "." counter(level6) " ";
}

.cherry[class*=theme__] .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: var(--base-editor-bg);
  color: var(--base-font-color);
  outline-color: var(--editor-header-color);
}

div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-tomorrow&languages=markup+css+clike+javascript */
  /**
   * prism.js tomorrow night eighties for JavaScript, CoffeeScript, CSS and HTML
   * Based on https://github.com/chriskempson/tomorrow-theme
   * <AUTHOR> Pritchard
   */
  /* Code blocks */
  /* Inline code */
}
div[data-type=codeBlock] code[class*=language-],
div[data-type=codeBlock] pre[class*=language-] {
  color: #ccc;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
}
div[data-type=codeBlock] :not(pre) > code[class*=language-],
div[data-type=codeBlock] pre[class*=language-] {
  background: #2d2d2d;
}
div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
div[data-type=codeBlock] .token.comment,
div[data-type=codeBlock] .token.block-comment,
div[data-type=codeBlock] .token.prolog,
div[data-type=codeBlock] .token.doctype,
div[data-type=codeBlock] .token.cdata {
  color: #999;
}
div[data-type=codeBlock] .token.punctuation {
  color: #ccc;
}
div[data-type=codeBlock] .token.tag,
div[data-type=codeBlock] .token.attr-name,
div[data-type=codeBlock] .token.namespace,
div[data-type=codeBlock] .token.deleted {
  color: #e2777a;
}
div[data-type=codeBlock] .token.function-name {
  color: #6196cc;
}
div[data-type=codeBlock] .token.boolean,
div[data-type=codeBlock] .token.number,
div[data-type=codeBlock] .token.function {
  color: #f08d49;
}
div[data-type=codeBlock] .token.property,
div[data-type=codeBlock] .token.class-name,
div[data-type=codeBlock] .token.constant,
div[data-type=codeBlock] .token.symbol {
  color: #f8c555;
}
div[data-type=codeBlock] .token.selector,
div[data-type=codeBlock] .token.important,
div[data-type=codeBlock] .token.atrule,
div[data-type=codeBlock] .token.keyword,
div[data-type=codeBlock] .token.builtin {
  color: #cc99cd;
}
div[data-type=codeBlock] .token.string,
div[data-type=codeBlock] .token.char,
div[data-type=codeBlock] .token.attr-value,
div[data-type=codeBlock] .token.regex,
div[data-type=codeBlock] .token.variable {
  color: #7ec699;
}
div[data-type=codeBlock] .token.operator,
div[data-type=codeBlock] .token.entity,
div[data-type=codeBlock] .token.url {
  color: #67cdcc;
}
div[data-type=codeBlock] .token.important,
div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
div[data-type=codeBlock] .token.entity {
  cursor: help;
}
div[data-type=codeBlock] .token.inserted {
  color: green;
}
div[data-code-wrap=wrap] div[data-type=codeBlock] code[class*=language-] {
  white-space: pre-wrap;
}
[data-code-block-theme=default] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js default theme for JavaScript, CSS and HTML
   * Based on dabblet (http://dabblet.com)
   * <AUTHOR> Verou
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  color: black;
  background: none;
  text-shadow: 0 1px white;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}
@media print {
  [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
}
[data-code-block-theme=default] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  background: #f5f2f0;
}
[data-code-block-theme=default] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=default] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=default] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=default] div[data-type=codeBlock] .token.cdata {
  color: slategray;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.punctuation {
  color: #999;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.property,
[data-code-block-theme=default] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=default] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=default] div[data-type=codeBlock] .token.number,
[data-code-block-theme=default] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=default] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=default] div[data-type=codeBlock] .token.deleted {
  color: #905;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=default] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=default] div[data-type=codeBlock] .token.string,
[data-code-block-theme=default] div[data-type=codeBlock] .token.char,
[data-code-block-theme=default] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=default] div[data-type=codeBlock] .token.inserted {
  color: #690;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=default] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=default] div[data-type=codeBlock] .token.url,
[data-code-block-theme=default] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=default] div[data-type=codeBlock] .style .token.string {
  color: #9a6e3a;
  /* This background color was intended by the author of this theme. */
  background: hsla(0, 0%, 100%, 0.5);
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=default] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=default] div[data-type=codeBlock] .token.keyword {
  color: #07a;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.function,
[data-code-block-theme=default] div[data-type=codeBlock] .token.class-name {
  color: #DD4A68;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=default] div[data-type=codeBlock] .token.important,
[data-code-block-theme=default] div[data-type=codeBlock] .token.variable {
  color: #e90;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.important,
[data-code-block-theme=default] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=dark] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-dark&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Dark theme for JavaScript, CSS and HTML
   * Based on the slides of the talk “/Reg(exp){2}lained/”
   * <AUTHOR> Verou
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=dark] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
  color: white;
  background: none;
  text-shadow: 0 -0.1em 0.2em black;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
@media print {
  [data-code-block-theme=dark] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  background: hsl(30, 20%, 25%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border: 0.3em solid hsl(30, 20%, 40%);
  border-radius: 0.5em;
  box-shadow: 1px 1px 0.5em black inset;
}
[data-code-block-theme=dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.15em 0.2em 0.05em;
  border-radius: 0.3em;
  border: 0.13em solid hsl(30, 20%, 40%);
  box-shadow: 1px 1px 0.3em -0.1em black inset;
  white-space: normal;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.cdata {
  color: hsl(30, 20%, 50%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.punctuation {
  opacity: 0.7;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.symbol {
  color: hsl(350, 40%, 70%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.inserted {
  color: hsl(75, 70%, 60%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.url,
[data-code-block-theme=dark] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.variable {
  color: hsl(40, 90%, 60%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.keyword {
  color: hsl(350, 40%, 70%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.important,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.deleted {
  color: red;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] {
  /**
   * One Dark theme for prism.js
   * Based on Atom's One Dark theme: https://github.com/atom/atom/tree/master/packages/one-dark-syntax
   */
  /**
   * One Dark colours (accurate as of commit 8ae45ca on 6 Sep 2018)
   * From colors.less
   * --mono-1: hsl(220, 14%, 71%);
   * --mono-2: hsl(220, 9%, 55%);
   * --mono-3: hsl(220, 10%, 40%);
   * --hue-1: hsl(187, 47%, 55%);
   * --hue-2: hsl(207, 82%, 66%);
   * --hue-3: hsl(286, 60%, 67%);
   * --hue-4: hsl(95, 38%, 62%);
   * --hue-5: hsl(355, 65%, 65%);
   * --hue-5-2: hsl(5, 48%, 51%);
   * --hue-6: hsl(29, 54%, 61%);
   * --hue-6-2: hsl(39, 67%, 69%);
   * --syntax-fg: hsl(220, 14%, 71%);
   * --syntax-bg: hsl(220, 13%, 18%);
   * --syntax-gutter: hsl(220, 14%, 45%);
   * --syntax-guide: hsla(220, 14%, 71%, 0.15);
   * --syntax-accent: hsl(220, 100%, 66%);
   * From syntax-variables.less
   * --syntax-selection-color: hsl(220, 13%, 28%);
   * --syntax-gutter-background-color-selected: hsl(220, 13%, 26%);
   * --syntax-cursor-line: hsla(220, 100%, 80%, 0.04);
   */
  /* Selection */
  /* Code blocks */
  /* Inline code */
  /* Print */
  /* HTML overrides */
  /* CSS overrides */
  /* JS overrides */
  /* JSON overrides */
  /* MD overrides */
  /* General */
  /* Plugin overrides */
  /* Selectors should have higher specificity than those in the plugins' default stylesheets */
  /* Show Invisibles plugin overrides */
  /* Toolbar plugin overrides */
  /* Space out all buttons and move them away from the right edge of the code block */
  /* Styling the buttons */
  /* Line Highlight plugin overrides */
  /* The highlighted line itself */
  /* Default line numbers in Line Highlight plugin */
  /* Hovering over a linkable line number (in the gutter area) */
  /* Requires Line Numbers plugin as well */
  /* Line Numbers and Command Line plugins overrides */
  /* Line separating gutter from coding area */
  /* Stuff in the gutter */
  /* Match Braces plugin overrides */
  /* Note: Outline colour is inherited from the braces */
  /* Diff Highlight plugin overrides */
  /* Taken from https://github.com/atom/github/blob/master/styles/variables.less */
  /* Previewers plugin overrides */
  /* Based on https://github.com/atom-community/atom-ide-datatip/blob/master/styles/atom-ide-datatips.less and https://github.com/atom/atom/blob/master/packages/one-dark-ui */
  /* Border around popup */
  /* Angle and time should remain as circles and are hence not included */
  /* Triangles pointing to the code */
  /* Background colour within the popup */
  /* For angle, this is the positive area (eg. 90deg will display one quadrant in this colour) */
  /* For time, this is the alternate colour */
  /* Stroke colours of the handle, direction point, and vector itself */
  /* Fill colour of the handle */
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
  background: hsl(220, 13%, 18%);
  color: hsl(220, 14%, 71%);
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: "Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  hyphens: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-]::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-] *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(220, 13%, 28%);
  color: inherit;
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-] *::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(220, 13%, 28%);
  color: inherit;
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-] *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] *::selection {
  background: hsl(220, 13%, 28%);
  color: inherit;
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em 0.3em;
  border-radius: 0.3em;
  white-space: normal;
}
@media print {
  [data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.cdata {
  color: hsl(220, 10%, 40%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.entity {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.atrule {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.keyword {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.important {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value > .token.punctuation {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.function {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.url {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value > .token.punctuation.attr-equals,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.selector {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.property {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.function,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.url > .token.function {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.url > .token.string.url {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.important,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.atrule .token.rule {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-javascript .token.operator {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(5, 48%, 51%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-json .token.operator {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-json .token.null.keyword {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.operator,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url-reference.url > .token.string {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.content {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.url,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url-reference.url {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.blockquote.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.hr.punctuation {
  color: hsl(220, 10%, 40%);
  font-style: italic;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.code-snippet {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.bold .token.content {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.italic .token.content {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.strike .token.content,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.strike .token.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.list.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.title.important > .token.punctuation {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.namespace {
  opacity: 0.8;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.tab:not(:empty):before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.cr:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.lf:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.space:before {
  color: hsla(220, 14%, 71%, 0.15);
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item {
  margin-right: 0.4em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span {
  background: hsl(220, 13%, 26%);
  color: hsl(220, 9%, 55%);
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus {
  background: hsl(220, 13%, 28%);
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: hsla(220, 100%, 80%, 0.04);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight[data-end]:after {
  background: hsl(220, 13%, 26%);
  color: hsl(220, 14%, 71%);
  padding: 0.1em 0.6em;
  border-radius: 0.3em;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2); /* same as Toolbar plugin default */
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before {
  background-color: hsla(220, 100%, 80%, 0.04);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .command-line .command-line-prompt {
  border-right-color: hsla(220, 14%, 71%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .command-line .command-line-prompt > span:before {
  color: hsl(220, 14%, 45%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-1,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-5,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-9 {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-2,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-6,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-10 {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-3,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-7,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-11 {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-4,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-8,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-12 {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix),
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) {
  background-color: hsla(353, 100%, 66%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix),
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) {
  background-color: hsla(137, 100%, 55%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection, [data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer.prism-previewer:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div {
  border-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-color.prism-previewer-color:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing:before {
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer.prism-previewer:after {
  border-top-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-flipped.prism-previewer-flipped.after {
  border-bottom-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing {
  background: hsl(219, 13%, 22%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle circle,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time circle {
  stroke: hsl(220, 14%, 71%);
  stroke-opacity: 1;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing path,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing line {
  stroke: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle {
  fill: transparent;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] {
  /**
   * One Light theme for prism.js
   * Based on Atom's One Light theme: https://github.com/atom/atom/tree/master/packages/one-light-syntax
   */
  /**
   * One Light colours (accurate as of commit eb064bf on 19 Feb 2021)
   * From colors.less
   * --mono-1: hsl(230, 8%, 24%);
   * --mono-2: hsl(230, 6%, 44%);
   * --mono-3: hsl(230, 4%, 64%)
   * --hue-1: hsl(198, 99%, 37%);
   * --hue-2: hsl(221, 87%, 60%);
   * --hue-3: hsl(301, 63%, 40%);
   * --hue-4: hsl(119, 34%, 47%);
   * --hue-5: hsl(5, 74%, 59%);
   * --hue-5-2: hsl(344, 84%, 43%);
   * --hue-6: hsl(35, 99%, 36%);
   * --hue-6-2: hsl(35, 99%, 40%);
   * --syntax-fg: hsl(230, 8%, 24%);
   * --syntax-bg: hsl(230, 1%, 98%);
   * --syntax-gutter: hsl(230, 1%, 62%);
   * --syntax-guide: hsla(230, 8%, 24%, 0.2);
   * --syntax-accent: hsl(230, 100%, 66%);
   * From syntax-variables.less
   * --syntax-selection-color: hsl(230, 1%, 90%);
   * --syntax-gutter-background-color-selected: hsl(230, 1%, 90%);
   * --syntax-cursor-line: hsla(230, 8%, 24%, 0.05);
   */
  /* Selection */
  /* Code blocks */
  /* Inline code */
  /* HTML overrides */
  /* CSS overrides */
  /* JS overrides */
  /* JSON overrides */
  /* MD overrides */
  /* General */
  /* Plugin overrides */
  /* Selectors should have higher specificity than those in the plugins' default stylesheets */
  /* Show Invisibles plugin overrides */
  /* Toolbar plugin overrides */
  /* Space out all buttons and move them away from the right edge of the code block */
  /* Styling the buttons */
  /* Line Highlight plugin overrides */
  /* The highlighted line itself */
  /* Default line numbers in Line Highlight plugin */
  /* Hovering over a linkable line number (in the gutter area) */
  /* Requires Line Numbers plugin as well */
  /* Line Numbers and Command Line plugins overrides */
  /* Line separating gutter from coding area */
  /* Stuff in the gutter */
  /* Match Braces plugin overrides */
  /* Note: Outline colour is inherited from the braces */
  /* Diff Highlight plugin overrides */
  /* Taken from https://github.com/atom/github/blob/master/styles/variables.less */
  /* Previewers plugin overrides */
  /* Based on https://github.com/atom-community/atom-ide-datatip/blob/master/styles/atom-ide-datatips.less and https://github.com/atom/atom/blob/master/packages/one-light-ui */
  /* Border around popup */
  /* Angle and time should remain as circles and are hence not included */
  /* Triangles pointing to the code */
  /* Background colour within the popup */
  /* For angle, this is the positive area (eg. 90deg will display one quadrant in this colour) */
  /* For time, this is the alternate colour */
  /* Stroke colours of the handle, direction point, and vector itself */
  /* Fill colour of the handle */
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] {
  background: hsl(230, 1%, 98%);
  color: hsl(230, 8%, 24%);
  font-family: "Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  hyphens: none;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-] *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(230, 1%, 90%);
  color: inherit;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-] *::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(230, 1%, 90%);
  color: inherit;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-] *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] *::selection {
  background: hsl(230, 1%, 90%);
  color: inherit;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em 0.3em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.cdata {
  color: hsl(230, 4%, 64%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.entity {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.atrule {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.keyword {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.important {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.string,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.char,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value > .token.punctuation {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.function {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.url {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value > .token.punctuation.attr-equals,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.selector {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.property {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.function,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.url > .token.function {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.url > .token.string.url {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.important,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.atrule .token.rule {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-javascript .token.operator {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(344, 84%, 43%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-json .token.operator {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-json .token.null.keyword {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.operator,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url-reference.url > .token.string {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.content {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.url,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url-reference.url {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.blockquote.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.hr.punctuation {
  color: hsl(230, 4%, 64%);
  font-style: italic;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.code-snippet {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.bold .token.content {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.italic .token.content {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.strike .token.content,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.strike .token.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.list.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.title.important > .token.punctuation {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.8;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.tab:not(:empty):before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.cr:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.lf:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.space:before {
  color: hsla(230, 8%, 24%, 0.2);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item {
  margin-right: 0.4em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span {
  background: hsl(230, 1%, 90%);
  color: hsl(230, 6%, 44%);
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus {
  background: hsl(230, 1%, 78%); /* custom: darken(--syntax-bg, 20%) */
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: hsla(230, 8%, 24%, 0.05);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight[data-end]:after {
  background: hsl(230, 1%, 90%);
  color: hsl(230, 8%, 24%);
  padding: 0.1em 0.6em;
  border-radius: 0.3em;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2); /* same as Toolbar plugin default */
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before {
  background-color: hsla(230, 8%, 24%, 0.05);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows,
[data-code-block-theme=one-light] div[data-type=codeBlock] .command-line .command-line-prompt {
  border-right-color: hsla(230, 8%, 24%, 0.2);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .command-line .command-line-prompt > span:before {
  color: hsl(230, 1%, 62%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-1,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-5,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-9 {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-2,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-6,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-10 {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-3,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-7,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-11 {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-4,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-8,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-12 {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix),
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) {
  background-color: hsla(353, 100%, 66%, 0.15);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix),
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) {
  background-color: hsla(137, 100%, 55%, 0.15);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection, [data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer.prism-previewer:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div {
  border-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-color.prism-previewer-color:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing:before {
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer.prism-previewer:after {
  border-top-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-flipped.prism-previewer-flipped.after {
  border-bottom-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing {
  background: hsl(0, 0%, 100%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle circle,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time circle {
  stroke: hsl(230, 8%, 24%);
  stroke-opacity: 1;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing path,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing line {
  stroke: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle {
  fill: transparent;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] {
  /*********************************************************
  * Tokens
  */
  /*********************************************************
  * Language Specific
  */
  /*********************************************************
  * Line highlighting
  */
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] {
  color: #d4d4d4;
  font-size: 13px;
  text-shadow: none;
  font-family: Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection, [data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] *::-moz-selection {
  text-shadow: none;
  background: #264F78;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-]::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] *::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] *::selection {
  text-shadow: none;
  background: #264F78;
}
@media print {
  [data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-],
  [data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  background: #1e1e1e;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em 0.3em;
  border-radius: 0.3em;
  color: #db4c69;
  background: #1e1e1e;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .namespace {
  opacity: 0.7;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.doctype .token.doctype-tag {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.doctype .token.name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.prolog {
  color: #6a9955;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .language-css .token.punctuation,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .language-javascript .token.punctuation {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.unit {
  color: #b5cea8;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.deleted {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-css .token.string.url {
  text-decoration: underline;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.entity {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.operator.arrow {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.rule {
  color: #c586c0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url .token.function {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url .token.punctuation {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword.module,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword.control-flow {
  color: #c586c0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.function,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.function .token.maybe-class-name {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.regex {
  color: #d16969;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.important {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.constant {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.maybe-class-name {
  color: #4ec9b0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.console {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.parameter {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.interpolation {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.punctuation.interpolation-punctuation {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.boolean {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.imports .token.maybe-class-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.exports .token.maybe-class-name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.selector {
  color: #d7ba7d;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.escape {
  color: #d7ba7d;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag .token.punctuation {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.cdata {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value .token.punctuation {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value .token.punctuation.attr-equals {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.entity {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.namespace {
  color: #4ec9b0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-javascript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-javascript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-jsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-jsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-typescript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-typescript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-tsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-tsx] {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-css],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-css] {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-html],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-html] {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-regex .token.anchor {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .token.punctuation {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] > code[class*=language-] {
  position: relative;
  z-index: 1;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: #f7ebc6;
  box-shadow: inset 5px 0 0 #f7d87c;
  z-index: 0;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] {
  /**
   * VS theme by Andrew Lock (https://andrewlock.net)
   * Inspired by Visual Studio syntax coloring
   */
  /* Code blocks */
  /* Inline code */
  /* overrides color-values for the Line Numbers plugin
   * http://prismjs.com/plugins/line-numbers/
   */
  /* overrides color-values for the Line Highlight plugin
  * http://prismjs.com/plugins/line-highlight/
  */
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] {
  color: #393A34;
  font-family: "Consolas", "Bitstream Vera Sans Mono", "Courier New", Courier, monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  font-size: 0.9em;
  line-height: 1.2em;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre > code[class*=language-] {
  font-size: 1em;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #C1DEF1;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #C1DEF1;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-] ::selection {
  background: #C1DEF1;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border: 1px solid #dddddd;
  background-color: white;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em;
  padding-top: 1px;
  padding-bottom: 1px;
  background: #f8f8f8;
  border: 1px solid #dddddd;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.cdata {
  color: #008000;
  font-style: italic;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.string {
  color: #A31515;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.operator {
  color: #393A34; /* no highlight */
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.url,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.inserted {
  color: #36acaa;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.selector,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.boolean,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.number,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-css] {
  color: #0000ff;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.function {
  color: #393A34;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.tag {
  color: #9a050f;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.keyword {
  color: #00009f;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.property {
  color: #2B91AF;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.selector {
  color: #800000;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.entity {
  color: #ff0000;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.directive.tag .tag {
  background: #ffff00;
  color: #393A34;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows {
  border-right-color: #a5a5a5;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before {
  color: #2B91AF;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: rgba(193, 222, 241, 0.2);
  background: linear-gradient(to right, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0));
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-okaidia&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * okaidia theme for JavaScript, CSS and HTML
   * Loosely based on Monokai textmate theme by http://www.monokai.nl/
   * <AUTHOR>
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  color: #f8f8f2;
  background: none;
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  background: #272822;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.cdata {
  color: #8292a2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.punctuation {
  color: #f8f8f2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.property,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.deleted {
  color: #f92672;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.number {
  color: #ae81ff;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.char,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.inserted {
  color: #a6e22e;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.url,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.variable {
  color: #f8f8f2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.function,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.class-name {
  color: #e6db74;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.keyword {
  color: #66d9ef;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.important {
  color: #fd971f;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.important,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-twilight&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Twilight theme
   * Based (more or less) on the Twilight theme originally of Textmate fame.
   * <AUTHOR> Bach
   */
  /* Code blocks */
  /* Text Selection colour */
  /* Inline code */
  /* Markup */
  /* Make the tokens sit above the line highlight so the colours don't look faded. */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] {
  color: white;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  text-shadow: 0 -0.1em 0.2em black;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=twilight] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  background: hsl(0, 0%, 8%); /* #141414 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] {
  border-radius: 0.5em;
  border: 0.3em solid hsl(0, 0%, 33%); /* #282A2B */
  box-shadow: 1px 1px 0.5em black inset;
  margin: 0.5em 0;
  overflow: auto;
  padding: 1em;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::-moz-selection {
  /* Firefox */
  background: hsl(200, 4%, 16%); /* #282A2B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::selection {
  /* Safari */
  background: hsl(200, 4%, 16%); /* #282A2B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: hsla(0, 0%, 93%, 0.15); /* #EDEDED */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: hsla(0, 0%, 93%, 0.15); /* #EDEDED */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-] ::selection {
  text-shadow: none;
  background: hsla(0, 0%, 93%, 0.15); /* #EDEDED */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  border-radius: 0.3em;
  border: 0.13em solid hsl(0, 0%, 33%); /* #545454 */
  box-shadow: 1px 1px 0.3em -0.1em black inset;
  padding: 0.15em 0.2em 0.05em;
  white-space: normal;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.cdata {
  color: hsl(0, 0%, 47%); /* #777777 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.punctuation {
  opacity: 0.7;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.number,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.deleted {
  color: hsl(14, 58%, 55%); /* #CF6A4C */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.property,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.builtin {
  color: hsl(53, 89%, 79%); /* #F9EE98 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.char,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.url,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.inserted {
  color: hsl(76, 21%, 52%); /* #8F9D6A */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.atrule {
  color: hsl(218, 22%, 55%); /* #7587A6 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.important {
  color: hsl(42, 75%, 65%); /* #E9C062 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.important,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[data-line] {
  padding: 1em 0 1em 3em;
  position: relative;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.tag,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.attr-name,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.punctuation {
  color: hsl(33, 33%, 52%); /* #AC885B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token {
  position: relative;
  z-index: 1;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight {
  background: hsla(0, 0%, 33%, 0.25); /* #545454 */
  background: linear-gradient(to right, hsla(0, 0%, 33%, 0.1) 70%, hsla(0, 0%, 33%, 0)); /* #545454 */
  border-bottom: 1px dashed hsl(0, 0%, 33%); /* #545454 */
  border-top: 1px dashed hsl(0, 0%, 33%); /* #545454 */
  left: 0;
  line-height: inherit;
  margin-top: 0.75em; /* Same as .prism’s padding-top */
  padding: inherit 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  white-space: pre;
  z-index: 0;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight:before,
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight[data-end]:after {
  background-color: hsl(215, 15%, 59%); /* #8794A6 */
  border-radius: 999px;
  box-shadow: 0 1px white;
  color: hsl(24, 20%, 95%); /* #F5F2F0 */
  content: attr(data-start);
  font: bold 65%/1.5 sans-serif;
  left: 0.6em;
  min-width: 1em;
  padding: 0 0.5em;
  position: absolute;
  text-align: center;
  text-shadow: none;
  top: 0.4em;
  vertical-align: 0.3em;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight[data-end]:after {
  bottom: 0.4em;
  content: attr(data-end);
  top: auto;
}
[data-code-block-theme=coy] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-coy&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Coy theme for JavaScript, CoffeeScript, CSS and HTML
   * Based on https://github.com/tshedor/workshop-wp-theme (Example: http://workshop.kansan.com/category/sessions/basics or http://workshop.timshedor.com/category/sessions/basics);
   * <AUTHOR>  Shedor
   */
  /* Code blocks */
  /* Margin bottom to accommodate shadow */
  /* Inline code */
  /* Plugin styles: Line Numbers */
  /* Plugin styles: Line Highlight */
}
[data-code-block-theme=coy] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  color: black;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  position: relative;
  margin: 0.5em 0;
  overflow-y: hidden;
  padding: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] > code {
  position: relative;
  border-left: 10px solid #358ccb;
  box-shadow: -1px 0px 0px 0px #358ccb, 0px 0px 0px 1px #dfdfdf;
  background-color: #fdfdfd;
  background-image: linear-gradient(transparent 50%, rgba(69, 142, 209, 0.04) 50%);
  background-size: 3em 3em;
  background-origin: content-box;
  background-attachment: local;
}
[data-code-block-theme=coy] div[data-type=codeBlock] code[class*=language-] {
  max-height: inherit;
  height: inherit;
  padding: 0 1em;
  display: block;
}
[data-code-block-theme=coy] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  background-color: #fdfdfd;
  box-sizing: border-box;
  margin-bottom: 1em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  position: relative;
  padding: 0.2em;
  border-radius: 0.3em;
  color: #c92c2c;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: inline;
  white-space: normal;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:before,
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
  content: "";
  z-index: -2;
  display: block;
  position: absolute;
  bottom: 0.75em;
  left: 0.18em;
  width: 40%;
  height: 20%;
  max-height: 13em;
  box-shadow: 0px 13px 8px #979797;
  transform: rotate(-2deg);
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
  right: 0.75em;
  left: auto;
  transform: rotate(2deg);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.block-comment,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.cdata {
  color: #7D8B99;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.punctuation {
  color: #5F6364;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.property,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.number,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.function-name,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.deleted {
  color: #c92c2c;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.string,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.char,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.function,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.inserted {
  color: #2f9c0a;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.url,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.variable {
  color: #a67f59;
  background: rgba(255, 255, 255, 0.5);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.class-name {
  color: #1990b8;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=coy] div[data-type=codeBlock] .style .token.string {
  color: #a67f59;
  background: rgba(255, 255, 255, 0.5);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.important {
  font-weight: normal;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
@media screen and (max-width: 767px) {
  [data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:before,
  [data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
    bottom: 14px;
    box-shadow: none;
  }
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers {
  padding-left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers code {
  padding-left: 3.8em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers .line-numbers-rows {
  left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-][data-line] {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[data-line] code {
  position: relative;
  padding-left: 4em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre .line-highlight {
  margin-top: 0;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-solarizedlight&languages=markup+css+clike+javascript */
  /*
   Solarized Color Schemes originally by Ethan Schoonover
   http://ethanschoonover.com/solarized

   Ported for PrismJS by Hector Matos
   Website: https://krakendev.io
   Twitter Handle: https://twitter.com/allonsykraken)
  */
  /*
  SOLARIZED HEX
  --------- -------
  base03    #002b36
  base02    #073642
  base01    #586e75
  base00    #657b83
  base0     #839496
  base1     #93a1a1
  base2     #eee8d5
  base3     #fdf6e3
  yellow    #b58900
  orange    #cb4b16
  red       #dc322f
  magenta   #d33682
  violet    #6c71c4
  blue      #268bd2
  cyan      #2aa198
  green     #859900
  */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  color: #657b83; /* base00 */
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  hyphens: none;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #073642; /* base02 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #073642; /* base02 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-] ::selection {
  background: #073642; /* base02 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  background-color: #fdf6e3; /* base3 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.cdata {
  color: #93a1a1; /* base1 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.punctuation {
  color: #586e75; /* base01 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.deleted {
  color: #268bd2; /* blue */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.string,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.char,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.url,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.inserted {
  color: #2aa198; /* cyan */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.entity {
  color: #657b83; /* base00 */
  background: #eee8d5; /* base2 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.keyword {
  color: #859900; /* green */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.function,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.class-name {
  color: #b58900; /* yellow */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.variable {
  color: #cb4b16; /* orange */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.entity {
  cursor: help;
}

.cherry-detail details {
  background: var(--accordion-bg);
  border: 1px solid var(--accordion-border);
  border-radius: calc(var(--accordion-radius) + 1px);
  box-shadow: var(--accordion-shadow);
  margin-bottom: var(--spacing-md);
  transition: box-shadow 0.2s ease;
  overflow: hidden;
}
.cherry-detail details:hover {
  box-shadow: var(--accordion-shadow);
}
.cherry-detail details summary {
  background: var(--accordion-summary-bg);
  color: var(--accordion-summary-color);
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) calc(var(--spacing-lg) + var(--spacing-xl));
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s ease;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border-radius: var(--accordion-radius) var(--accordion-radius) 0 0;
}
.cherry-detail details summary:hover {
  background: var(--accordion-summary-hover-bg);
}
.cherry-detail details summary::marker {
  content: "";
}
.cherry-detail details summary::before {
  content: "▼";
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.2s ease;
}
.cherry-detail details[open] summary::before {
  transform: translateY(-50%) rotate(180deg);
}
.cherry-detail details:not([open]) summary {
  border-radius: var(--accordion-radius);
}
.cherry-detail details .cherry-detail-body {
  padding: var(--spacing-lg);
  background: var(--accordion-body-bg);
  border-top: 1px solid var(--accordion-body-border);
  color: var(--accordion-body-color);
  line-height: var(--line-height-relaxed);
  border-radius: 0 0 var(--accordion-radius) var(--accordion-radius);
}
.cherry-detail details .cherry-detail-body > *:first-child {
  margin-top: 0;
}
.cherry-detail details .cherry-detail-body > *:last-child {
  margin-bottom: 0;
}

.cherry-detail__multiple {
  border-radius: calc(var(--accordion-radius) + 1px);
  box-shadow: var(--accordion-shadow);
  background: var(--accordion-bg);
  border: 1px solid var(--accordion-border);
  overflow: hidden;
}
.cherry-detail__multiple details {
  margin-bottom: 0;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid var(--accordion-body-border);
  box-shadow: none;
}
.cherry-detail__multiple details:first-child summary {
  border-radius: var(--accordion-radius) var(--accordion-radius) 0 0;
}
.cherry-detail__multiple details:last-child {
  border-bottom: none;
}
.cherry-detail__multiple details:last-child:not([open]) summary {
  border-radius: 0 0 var(--accordion-radius) var(--accordion-radius);
}
.cherry-detail__multiple details:last-child[open] summary {
  border-radius: 0;
}
.cherry-detail__multiple details:last-child .cherry-detail-body {
  border-radius: 0 0 var(--accordion-radius) var(--accordion-radius);
}
.cherry-detail__multiple details:not(:first-child):not(:last-child) summary {
  border-radius: 0;
}
.cherry-detail__multiple details:not(:first-child):not(:last-child) .cherry-detail-body {
  border-radius: 0;
}
.cherry-detail__multiple details:hover {
  box-shadow: none;
}

.cherry-text-align__center table {
  margin-left: auto;
  margin-right: auto;
}

.cherry-text-align__right table {
  margin-left: auto;
}

.cherry-panel {
  margin: var(--panel-margin, 10px 0);
  overflow: hidden;
  border-radius: var(--panel-border-radius, var(--radius-xl));
  box-sizing: border-box;
  border: var(--panel-border, 0.5px solid var(--panel-border-color, transparent));
  background: var(--panel-bg, transparent);
  box-shadow: var(--panel-box-shadow, none);
  backdrop-filter: var(--panel-backdrop-filter, none);
}
.cherry-panel .cherry-panel--title {
  color: var(--panel-title-color, #fff);
  padding: var(--panel-title-padding, 5px 20px);
  background: var(--panel-title-bg, transparent);
  border-radius: var(--panel-title-border-radius, 0);
  border-bottom: var(--panel-title-border-bottom, none);
}
.cherry-panel .cherry-panel--title.cherry-panel--title__not-empty::before {
  font-family: "ch-icon";
  margin: 0 var(--spacing-md) 0 -6px;
  vertical-align: bottom;
  content: var(--panel-icon, "");
}
.cherry-panel .cherry-panel--body {
  padding: var(--panel-body-padding, 5px 20px);
  background: var(--panel-body-bg, transparent);
  color: var(--panel-body-color, inherit);
  border-radius: var(--panel-body-border-radius, 0);
}

.cherry-panel__primary {
  --panel-bg: var(--panel-primary-bg, #cfe2ff);
  --panel-border-color: var(--panel-primary-border-color, transparent);
  --panel-box-shadow: var(--panel-primary-box-shadow, none);
  --panel-title-bg: var(--panel-primary-title-bg, #0d6dfe);
  --panel-title-color: var(--panel-primary-title-color, #fff);
  --panel-title-border-radius: var(--panel-primary-title-border-radius, 0);
  --panel-title-border-bottom: var(--panel-primary-title-border-bottom, none);
  --panel-body-bg: var(--panel-primary-body-bg, transparent);
  --panel-body-color: var(--panel-primary-body-color, #0a58ca);
  --panel-body-border-radius: var(--panel-primary-body-border-radius, 0);
  --panel-icon: var(--panel-primary-icon, "");
}

.cherry-panel__info {
  --panel-bg: var(--panel-info-bg, #cff4fc);
  --panel-border-color: var(--panel-info-border-color, transparent);
  --panel-box-shadow: var(--panel-info-box-shadow, none);
  --panel-title-bg: var(--panel-info-title-bg, #099cba);
  --panel-title-color: var(--panel-info-title-color, #fff);
  --panel-title-border-radius: var(--panel-info-title-border-radius, 0);
  --panel-title-border-bottom: var(--panel-info-title-border-bottom, none);
  --panel-body-bg: var(--panel-info-body-bg, transparent);
  --panel-body-color: var(--panel-info-body-color, #087990);
  --panel-body-border-radius: var(--panel-info-body-border-radius, 0);
  --panel-icon: var(--panel-info-icon, "");
}

.cherry-panel__warning {
  --panel-bg: var(--panel-warning-bg, #fff3cd);
  --panel-border-color: var(--panel-warning-border-color, transparent);
  --panel-box-shadow: var(--panel-warning-box-shadow, none);
  --panel-title-bg: var(--panel-warning-title-bg, #b38806);
  --panel-title-color: var(--panel-warning-title-color, #fff);
  --panel-title-border-radius: var(--panel-warning-title-border-radius, 0);
  --panel-title-border-bottom: var(--panel-warning-title-border-bottom, none);
  --panel-body-bg: var(--panel-warning-body-bg, transparent);
  --panel-body-color: var(--panel-warning-body-color, #997404);
  --panel-body-border-radius: var(--panel-warning-body-border-radius, 0);
  --panel-icon: var(--panel-warning-icon, "");
}

.cherry-panel__danger {
  --panel-bg: var(--panel-danger-bg, #f8d7da);
  --panel-border-color: var(--panel-danger-border-color, transparent);
  --panel-box-shadow: var(--panel-danger-box-shadow, none);
  --panel-title-bg: var(--panel-danger-title-bg, #dc3545);
  --panel-title-color: var(--panel-danger-title-color, #fff);
  --panel-title-border-radius: var(--panel-danger-title-border-radius, 0);
  --panel-title-border-bottom: var(--panel-danger-title-border-bottom, none);
  --panel-body-bg: var(--panel-danger-body-bg, transparent);
  --panel-body-color: var(--panel-danger-body-color, #b02a37);
  --panel-body-border-radius: var(--panel-danger-body-border-radius, 0);
  --panel-icon: var(--panel-danger-icon, "");
}

.cherry-panel__success {
  --panel-bg: var(--panel-success-bg, #d1e7dd);
  --panel-border-color: var(--panel-success-border-color, transparent);
  --panel-box-shadow: var(--panel-success-box-shadow, none);
  --panel-title-bg: var(--panel-success-title-bg, #198754);
  --panel-title-color: var(--panel-success-title-color, #fff);
  --panel-title-border-radius: var(--panel-success-title-border-radius, 0);
  --panel-title-border-bottom: var(--panel-success-title-border-bottom, none);
  --panel-body-bg: var(--panel-success-body-bg, transparent);
  --panel-body-color: var(--panel-success-body-color, #146c43);
  --panel-body-border-radius: var(--panel-success-body-border-radius, 0);
  --panel-icon: var(--panel-success-icon, "");
}

.cherry .doing-resize-img {
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.cherry .cherry-previewer img {
  transition: all 0.1s;
}
.cherry .cherry-previewer-footnote-ref-hover-handler {
  position: absolute;
  min-width: 200px;
  max-width: 500px;
  padding: 10px;
  border-radius: 5px;
  z-index: 11;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  background: var(--base-editor-bg);
  border: 1px solid var(--base-border-color);
}
.cherry .footnote.hidden {
  display: none;
}
.cherry .cherry-previewer-img-size-handler {
  position: absolute;
  box-shadow: 0 1px 4px 0 rgba(20, 81, 154, 0.5);
  border: 1px solid var(--primary-color);
  box-sizing: content-box;
  pointer-events: none;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points {
  position: absolute;
  height: 10px;
  width: 10px;
  margin-top: -7px;
  margin-left: -7px;
  border-radius: 9px;
  background: var(--primary-color);
  border: 2px solid var(--oc-white);
  box-sizing: content-box;
  box-shadow: 0px 2px 2px 0px rgba(20, 81, 154, 0.5);
  pointer-events: all;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__background {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  opacity: 0.5;
  width: 100%;
  height: 100%;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftTop {
  cursor: nw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightTop {
  cursor: sw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftBottom {
  cursor: sw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightBottom {
  cursor: nw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-middleTop {
  cursor: n-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-middleBottom {
  cursor: n-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftMiddle {
  cursor: e-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightMiddle {
  cursor: e-resize;
}
.cherry .cherry-previewer-img-tool-handler {
  padding: var(--bubble-padding);
  display: flex;
  flex-direction: column;
}
.cherry .cherry-previewer-img-tool-handler .img-tool-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--bubble-btn-height);
  height: var(--bubble-btn-height);
  min-width: var(--bubble-btn-height);
  min-height: var(--bubble-btn-height);
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  border: var(--border-width-1) solid transparent;
  border-radius: var(--bubble-radius);
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: var(--font-size-md);
  transition: color 150ms, background-color 150ms, border-color 150ms;
  box-sizing: border-box;
  padding: 0;
}
.cherry .cherry-previewer-img-tool-handler .img-tool-button:hover {
  border-color: var(--toolbar-border);
  background-color: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
}
.cherry .cherry-previewer-img-tool-handler .img-tool-button.active {
  background: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
  border-color: var(--toolbar-border);
  box-shadow: 0 0 0 2px var(--primary-color);
  z-index: 1;
}
.cherry .cherry-previewer-img-tool-handler .img-tool-divider {
  width: 100%;
  margin: var(--border-width-4) 0;
  height: var(--border-width-1);
  border-bottom: var(--border-width-1) var(--toolbar-btn-color) dashed;
}
.cherry .cherry-previewer-img-tool-handler .img-tool-icon {
  font-size: 18px;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.cherry .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input {
  position: absolute;
}
.cherry .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  width: 100%;
  height: 100%;
  border: 0;
  box-sizing: border-box;
  resize: none;
  outline: 1px solid var(--primary-color);
  word-break: break-all;
}
.cherry .cherry-previewer-codeBlock-content-handler .cherry-previewer-codeBlock-content-handler__input {
  position: absolute;
}
.cherry .cherry-previewer-table-hover-handler {
  position: absolute;
  pointer-events: none;
  z-index: 999;
}
.cherry .cherry-previewer-table-hover-handler-container, .cherry .cherry-previewer-table-hover-handler-sort-container, .cherry .cherry-previewer-table-hover-handler-delete-container {
  position: absolute;
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.cherry .cherry-previewer-table-hover-handler__symbol {
  pointer-events: auto;
  display: flex;
  justify-content: center;
  position: absolute;
  color: var(--primary-color);
  width: 12px;
  height: 12px;
  line-height: 12px;
  border: 1px solid rgba(53, 130, 251, 0);
  background-color: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__symbol:hover {
  background-color: rgba(53, 130, 251, 0.53);
  color: var(--oc-white);
}
.cherry .cherry-previewer-table-hover-handler__sort {
  pointer-events: auto;
  display: flex;
  justify-content: center;
  position: absolute;
  color: var(--primary-color);
  width: 12px;
  height: 12px;
  padding: 5px 0;
  line-height: 12px;
  border: 1px solid rgba(53, 130, 251, 0);
  background-color: transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__sort:hover {
  background-color: rgba(53, 130, 251, 0.53);
  border-color: rgba(53, 130, 251, 0.53);
  color: var(--oc-white);
}
.cherry .cherry-previewer-table-hover-handler__sort[data-type=ColUp], .cherry .cherry-previewer-table-hover-handler__sort[data-type=ColDown] {
  padding: 0 5px;
}
.cherry .cherry-previewer-table-hover-handler__delete {
  pointer-events: auto;
  position: absolute;
  width: 25px;
  height: 15px;
  font-size: var(--font-size-xs);
  line-height: var(--font-size-xs);
  border: 1px solid var(--color-error);
  border-radius: 3px;
  background-color: transparent;
  color: var(--color-error);
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__delete:hover {
  background-color: var(--color-error);
  border-color: var(--color-error);
  color: var(--oc-white);
}
.cherry .cherry-previewer-table-hover-handler__delete[data-type=left], .cherry .cherry-previewer-table-hover-handler__delete[data-type=right] {
  padding: 0;
  width: 18px;
  height: 18px;
}
@keyframes changeBgColor {
  0% {
    background-color: rgba(255, 255, 204, 0.5333333333);
  }
  60% {
    background-color: rgba(255, 255, 204, 0.5333333333);
  }
  100% {
    background-color: rgba(255, 255, 204, 0);
  }
}
.cherry .cherry-highlight-line {
  animation: changeBgColor 1s;
}

@media print {
  img, figure, pre, table {
    page-break-inside: avoid;
  }
  .cherry-previewer {
    width: 100% !important;
    max-height: none;
    border-left: none !important;
  }
  .cherry-toolbar, .cherry-sidebar, .cherry-editor, .cherry-drag {
    display: none !important;
  }
  body > *:not(.cherry-export-wrapper):not([class*=cherry-export-wrapper]) {
    display: none !important;
  }
  .cherry-export-wrapper,
  [class*=cherry-export-wrapper] {
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
}
.cherry-insert-formula-wrappler {
  width: 680px !important;
  height: 420px !important;
  padding: 0 !important;
  display: flex;
  position: fixed !important;
  z-index: 9999999;
  box-shadow: var(--box-shadow-down);
  box-sizing: border-box;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--base-border-color);
}
.cherry-insert-formula-wrappler ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.cherry-insert-formula-wrappler .cherry-formula-nav {
  width: 120px;
  flex-shrink: 0;
  padding: 8px;
  border-right: 1px solid var(--base-border-color);
  position: relative;
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-formula-main-tabs .cherry-formula-main-tab {
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--base-font-color);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-formula-main-tabs .cherry-formula-main-tab:not(:last-child) {
  margin-bottom: 4px;
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-formula-main-tabs .cherry-formula-main-tab.active {
  background-color: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-formula-main-tabs .cherry-formula-main-tab:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-insert-formula-more {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--editor-comment-color);
  white-space: nowrap;
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-insert-formula-more a {
  color: var(--primary-color);
  text-decoration: none;
}
.cherry-insert-formula-wrappler .cherry-formula-nav .cherry-insert-formula-more a:hover {
  text-decoration: underline;
}
.cherry-insert-formula-wrappler .cherry-formula-content-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}
.cherry-insert-formula-wrappler .cherry-formula-content {
  display: none;
  flex-direction: column;
  height: 100%;
}
.cherry-insert-formula-wrappler .cherry-formula-content.active {
  display: flex;
}
.cherry-insert-formula-wrappler .cherry-formula-sub-nav {
  padding: 8px 12px;
  flex-shrink: 0;
}
.cherry-insert-formula-wrappler .cherry-formula-sub-nav .cherry-formula-sub-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.cherry-insert-formula-wrappler .cherry-formula-sub-nav .cherry-formula-sub-tabs .cherry-formula-sub-tab {
  padding: 6px 12px;
  border-radius: 16px;
  cursor: pointer;
  font-size: 13px;
  color: var(--base-font-color);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.cherry-insert-formula-wrappler .cherry-formula-sub-nav .cherry-formula-sub-tabs .cherry-formula-sub-tab.active {
  background-color: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
}
.cherry-insert-formula-wrappler .cherry-formula-sub-nav .cherry-formula-sub-tabs .cherry-formula-sub-tab:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-insert-formula-wrappler .cherry-formula-grid-wrapper {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
}
.cherry-insert-formula-wrappler .cherry-formula-grid {
  display: none;
}
.cherry-insert-formula-wrappler .cherry-formula-grid.active {
  display: block;
}
.cherry-insert-formula-wrappler .cherry-formula-grid-group:not(:last-child) {
  margin-bottom: 20px;
}
.cherry-insert-formula-wrappler .cherry-formula-grid-group-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--base-font-color);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--base-border-color);
}
.cherry-insert-formula-wrappler .cherry-formula-grid-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.cherry-insert-formula-wrappler .cherry-formula-item {
  display: flex;
  align-items: center;
  border: 1px solid var(--oc-gray-4);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  background-color: var(--base-editor-bg);
}
.cherry-insert-formula-wrappler .cherry-formula-item:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-insert-formula-wrappler .cherry-formula-item svg,
.cherry-insert-formula-wrappler .cherry-formula-item img {
  pointer-events: none;
}
.cherry-insert-formula-wrappler .cherry-formula-item-light {
  min-height: 32px;
  width: auto;
  padding: 0 12px;
  justify-content: center;
}
.cherry-insert-formula-wrappler .cherry-formula-item-light svg,
.cherry-insert-formula-wrappler .cherry-formula-item-light img {
  width: auto;
}
.cherry-insert-formula-wrappler .cherry-formula-item-large {
  width: 100%;
  height: auto;
  min-height: 48px;
  padding: 10px 12px;
  justify-content: center;
}
.cherry-insert-formula-wrappler .cherry-formula-item-large svg,
.cherry-insert-formula-wrappler .cherry-formula-item-large img {
  height: auto;
  width: auto;
  max-width: 100%;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.formula-utils-bubble-container {
  width: 350px;
  height: 40px;
  background-color: #fff;
  position: fixed;
  left: 0;
  top: 0;
  display: none;
  z-index: 1000;
  box-sizing: border-box;
}
.formula-utils-bubble-container .formula-utils-btn {
  flex: 1;
  position: relative;
}
.formula-utils-bubble-container .formula-utils-btn > button {
  width: 100%;
  height: 100%;
  border: 1px solid #fff;
  background-color: #ebecf2;
  cursor: pointer;
  border-radius: 5px;
}
.formula-utils-bubble-container .formula-utils-btn > button:hover {
  background-color: #eee;
}
.formula-utils-bubble-container .formula-utils-btn > button:focus {
  outline: none;
}
.formula-utils-bubble-container .formula-utils-btn > button:active {
  background-color: #ddd;
}
.formula-utils-bubble-container .formula-utils-btn > button:hover + .formula-utils-submenu {
  display: block;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu {
  position: absolute;
  display: none;
  width: 100%;
  background-color: #fff;
  border: 1px solid #f8f9fa;
  left: 0;
  top: 100%;
  box-shadow: 0 0 5px #f8f9fa;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu:hover {
  display: block;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div {
  width: 100%;
  height: 40px;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button {
  width: 100%;
  height: 100%;
  border: 1px solid #fff;
  background-color: #fff;
  cursor: pointer;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:hover {
  background-color: #eee;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:focus {
  outline: none;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:active {
  background-color: #ddd;
}

.cherry {
  --shortcut-key-config-panel-bg: var(--dropdown-bg);
  --shortcut-key-config-panel-text-color: var(--dropdown-item-color);
  --shortcut-panel-padding-x: 15px;
  --shortcut-panel-padding-y: 10px;
  --shortcut-panel-font-size-xs: var(--font-size-xs);
  --shortcut-panel-border-radius: 4px;
  --shortcut-panel-transition: all 0.2s;
}
.cherry .cherry-shortcut-key-config-panel-wrapper {
  background-color: var(--shortcut-key-config-panel-bg);
  color: var(--shortcut-key-config-panel-text-color);
  width: 300px !important;
  height: 518px !important;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-tabs {
  display: flex;
  padding: 4px var(--shortcut-panel-padding-x);
  gap: 10px;
  margin-bottom: 6px;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-tabs .shortcut-tab {
  flex: 1;
  padding: 6px 0 4px;
  border-radius: var(--shortcut-panel-border-radius);
  text-align: center;
  cursor: pointer;
  font-size: 12px;
  transition: var(--shortcut-panel-transition);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  background-color: var(--dropdown-item-hover-bg);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-tabs .shortcut-tab:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-tabs .shortcut-tab.active {
  color: var(--primary-color);
  background-color: var(--secondary-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-panels {
  flex: 1;
  overflow: auto;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-panels::-webkit-scrollbar {
  display: none;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-panels .shortcut-panel {
  display: none;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-panels .shortcut-panel.active {
  display: block;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul {
  list-style: none;
  padding: 0;
  margin: 0;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item {
  display: flex;
  align-items: center;
  height: auto;
  padding: 2px var(--shortcut-panel-padding-x);
  position: relative;
  color: var(--shortcut-key-config-panel-text-color);
  justify-content: flex-start;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item.editing,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item.editing {
  background-color: var(--dropdown-item-hover-bg);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item.editing .shortcut-key-config-panel-kbd,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item.editing .shortcut-key-config-panel-kbd {
  border: 1px dashed var(--primary-color);
  border-radius: var(--shortcut-panel-border-radius);
  padding: 2px 4px;
  background: var(--secondary-color);
  transform: translateX(0) !important;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item.editing .edit-btn,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item.editing .edit-btn {
  display: none;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item.editing .edit-actions,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item.editing .edit-actions {
  display: flex;
  align-items: center;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-config-panel-name,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-name {
  max-width: 120px;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-right,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-right-static,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-right,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-right-static {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
  justify-content: flex-end;
  position: relative;
  flex-direction: row;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-btn,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-btn {
  position: static;
  opacity: 0;
  visibility: hidden;
  transition: var(--shortcut-panel-transition);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: var(--shortcut-panel-border-radius);
  color: var(--dropdown-item-hover-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-btn:hover,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-btn:hover {
  color: var(--primary-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-config-panel-kbd,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-kbd {
  display: flex;
  align-items: center;
  gap: 2px;
  justify-content: right;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-config-panel-kbd .keyboard-key,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-kbd .keyboard-key {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--shortcut-key-config-panel-bg);
  color: var(--shortcut-key-config-panel-text-color);
  border: 1px solid #ccc;
  border-bottom: 2px solid #b5b5b5;
  border-radius: 6px;
  font-size: 13px;
  margin: 0 2px;
  padding: 3px 5px;
  line-height: 16px;
  min-width: 16px;
  text-align: center;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .shortcut-key-config-panel-kbd .shortcut-split,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-kbd .shortcut-split {
  color: var(--oc-gray-6);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions {
  margin-right: 4px;
  display: none;
  gap: 4px;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn {
  padding: 4px 6px 0;
  border-radius: var(--shortcut-panel-border-radius);
  cursor: pointer;
  transition: var(--shortcut-panel-transition);
  font-size: 12px;
  text-align: center;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn .ch-icon,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn .ch-icon {
  margin-right: 0;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn.save,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn.save {
  color: var(--color-success);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn.save:hover,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn.save:hover {
  background: var(--oc-lime-0);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn.cancel,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn.cancel {
  color: var(--color-error);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item .edit-actions .action-btn.cancel:hover,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .edit-actions .action-btn.cancel:hover {
  background: var(--oc-red-2);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item:hover,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item:hover {
  color: var(--dropdown-item-hover-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item:hover .edit-btn,
.cherry .cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item:hover .edit-btn {
  opacity: 1;
  visibility: visible;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-tips {
  font-size: var(--shortcut-panel-font-size-xs);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--shortcut-key-config-panel-text-color);
  text-align: center;
  padding: 8px var(--shortcut-panel-padding-x) 4px;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings {
  padding: var(--shortcut-panel-padding-y) var(--shortcut-panel-padding-x);
  font-size: var(--shortcut-panel-font-size-xs);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--shortcut-key-config-panel-text-color);
  cursor: pointer;
  justify-content: space-between;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  box-shadow: var(--accordion-shadow);
  margin-bottom: 5px;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings .shortcut-settings-btn {
  height: auto;
  line-height: 1.2em;
  text-wrap: nowrap;
}
.cherry .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings .shortcut-settings-btn:hover {
  color: var(--primary-color);
}
.cherry .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings .j-shortcut-settings-disable-btn:hover {
  color: var(--color-error);
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul {
  opacity: 0.3;
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item,
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .shortcut-key-item {
  cursor: default;
  pointer-events: none;
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item:hover,
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .shortcut-key-item:hover {
  color: inherit;
  background-color: transparent;
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item:hover .shortcut-key-config-panel-kbd,
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .shortcut-key-item:hover .shortcut-key-config-panel-kbd {
  transform: translateX(0);
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .cherry-dropdown-item:hover .edit-btn,
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul .shortcut-key-item:hover .edit-btn {
  opacity: 0;
  visibility: hidden;
}
.cherry .cherry-shortcut-key-config-panel-wrapper.disable .j-shortcut-settings-disable-btn {
  color: var(--color-error);
}

.cherry {
  display: flex;
  flex-flow: row wrap;
  align-items: stretch;
  align-content: flex-start;
  height: 100%;
  min-height: 60px;
  position: relative;
}
.cherry .cherry-editor,
.cherry .cherry-previewer {
  max-height: calc(100% - var(--height-toolbar));
  min-height: calc(100% - var(--height-toolbar));
}
.cherry .CodeMirror {
  height: 100%;
}
.cherry.cherry--no-toolbar .cherry-toolbar,
.cherry.cherry--no-toolbar .cherry-sidebar {
  height: 0;
  display: none;
}
.cherry.cherry--no-toolbar .cherry-editor,
.cherry.cherry--no-toolbar .cherry-previewer {
  max-height: 100%;
  min-height: 100%;
}

.cherry {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-md);
  line-height: var(--md-paragraph-line-height);
  color: var(--base-font-color);
  background: var(--base-editor-bg);
  box-shadow: var(--shadow-md);
}
.cherry .ch-icon {
  vertical-align: middle;
}
.cherry .clearfix {
  zoom: 1;
}
.cherry .clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
  font-size: 0;
}
.cherry.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.cherry .no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.cherry .cherry-insert-table-menu {
  display: block;
  position: fixed;
  top: 40px;
  left: 40px;
  border-collapse: separate;
  box-shadow: var(--shadow-md);
  padding: 4px;
  border-radius: var(--radius-md);
  width: auto;
  height: auto;
}
.cherry .cherry-insert-table-menu-item {
  padding: 7px;
  border: 1px solid var(--base-border-color);
}
.cherry .cherry-insert-table-menu-item.active {
  background-color: var(--toolbar-btn-hover-bg);
}

.cherry-dropdown {
  position: absolute;
  width: 130px;
  min-height: 40px;
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  box-shadow: var(--dropdown-shadow);
  padding: var(--dropdown-padding);
  border-radius: var(--dropdown-radius);
  margin-left: -60px;
  z-index: 13;
}
.cherry-dropdown-item {
  width: calc(100% - 8px);
  padding: var(--dropdown-item-padding);
  text-align: left;
  display: block;
  min-height: var(--dropdown-item-height);
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-sm);
  font-style: normal;
  cursor: pointer;
  box-sizing: border-box;
  color: var(--dropdown-item-color);
  background: var(--dropdown-bg);
  border-radius: var(--dropdown-item-radius);
  margin: 2px 4px;
  transition: color 150ms ease, background-color 150ms ease, border-color 150ms ease;
}
.cherry-dropdown-item:hover {
  background: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-dropdown-item__selected {
  background: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
}
.cherry-dropdown-item .ch-icon {
  margin-right: 8px;
  vertical-align: middle;
}
.cherry-dropdown-separator {
  height: 1px;
  background-color: var(--oc-gray-3);
  margin: var(--spacing-xs) var(--spacing-sm);
  opacity: 0.6;
}

.cherry-toolbar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--toolbar-padding);
  font-size: var(--toolbar-font-size);
  line-height: 2.8;
  min-height: var(--toolbar-min-height);
  flex-basis: 100%;
  box-sizing: border-box;
  z-index: 2;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: var(--toolbar-shadow);
  background: var(--toolbar-bg);
  border-radius: var(--toolbar-radius);
  overflow: hidden;
}
.cherry-toolbar .icon-loading.loading {
  display: inline-block;
  width: var(--spacing-sm);
  height: var(--spacing-sm);
}
.cherry-toolbar .icon-loading.loading:after {
  content: " ";
  display: block;
  width: var(--spacing-sm);
  height: var(--spacing-sm);
  margin-left: 2px;
  margin-top: -2px;
  border-radius: 50%;
  border: 2px solid var(--toolbar-btn-color);
  border-color: var(--toolbar-btn-color) transparent var(--toolbar-btn-color) transparent;
  animation: loading 1.2s linear infinite;
}
.cherry-toolbar .toolbar-left,
.cherry-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}
.cherry-toolbar .toolbar-left {
  margin-right: var(--toolbar-padding);
}
.cherry-toolbar .toolbar-right {
  flex: 0 1 auto;
  flex-direction: row-reverse;
  margin-left: calc(var(--toolbar-padding) / 2);
  box-sizing: border-box;
  min-height: 0;
}
.cherry-toolbar.preview-only .cherry-toolbar-button {
  display: none;
}
.cherry-toolbar.preview-only .cherry-toolbar-switchPreview {
  display: inline;
}
.cherry-toolbar-button {
  float: left;
  height: var(--toolbar-btn-height);
  padding: var(--toolbar-btn-padding);
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  border-radius: var(--toolbar-btn-radius);
  transition: color 150ms ease, background-color 150ms ease, border-color 150ms ease;
  cursor: pointer;
  font-style: normal;
}
.cherry-toolbar-button:hover {
  color: var(--toolbar-btn-hover-color);
  background: var(--toolbar-btn-hover-bg);
}
.cherry-toolbar-button.cherry-toolbar-split {
  font-size: 0;
  height: 19px;
  padding: 0 !important;
  margin: 0px 4px;
  border: 1px solid transparent;
  border-left: 1px solid var(--toolbar-split-color);
  pointer-events: none;
  overflow: hidden;
  opacity: 0.5;
}
.cherry-toolbar-button.disabled {
  color: var(--toolbar-btn-disabled);
}
.cherry-toolbar .cherry-toolbar,
.cherry-toolbar .cherry-floatmenu,
.cherry-toolbar .cherry-bubble,
.cherry-toolbar .cherry .cherry-previewer-img-tool-handler,
.cherry .cherry-toolbar .cherry-previewer-img-tool-handler,
.cherry-toolbar .cherry-sidebar {
  background: var(--toolbar-bg);
  border-color: var(--toolbar-border);
}
.cherry-toolbar .cherry-toolbar .cherry-toolbar-button,
.cherry-toolbar .cherry-floatmenu .cherry-toolbar-button,
.cherry-toolbar .cherry-bubble .cherry-toolbar-button,
.cherry-toolbar .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button,
.cherry .cherry-toolbar .cherry-previewer-img-tool-handler .cherry-toolbar-button,
.cherry-toolbar .cherry-sidebar .cherry-toolbar-button {
  color: var(--toolbar-btn-color);
}
.cherry-toolbar .cherry-toolbar .cherry-toolbar-button i,
.cherry-toolbar .cherry-floatmenu .cherry-toolbar-button i,
.cherry-toolbar .cherry-bubble .cherry-toolbar-button i,
.cherry-toolbar .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button i,
.cherry .cherry-toolbar .cherry-previewer-img-tool-handler .cherry-toolbar-button i,
.cherry-toolbar .cherry-sidebar .cherry-toolbar-button i {
  color: var(--toolbar-btn-color);
}
.cherry-toolbar .cherry-toolbar .cherry-toolbar-button:hover,
.cherry-toolbar .cherry-floatmenu .cherry-toolbar-button:hover,
.cherry-toolbar .cherry-bubble .cherry-toolbar-button:hover,
.cherry-toolbar .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button:hover,
.cherry .cherry-toolbar .cherry-previewer-img-tool-handler .cherry-toolbar-button:hover,
.cherry-toolbar .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color) !important;
  border-color: var(--toolbar-border);
}
.cherry-toolbar .cherry-toolbar .cherry-toolbar-button:hover i,
.cherry-toolbar .cherry-floatmenu .cherry-toolbar-button:hover i,
.cherry-toolbar .cherry-bubble .cherry-toolbar-button:hover i,
.cherry-toolbar .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button:hover i,
.cherry .cherry-toolbar .cherry-previewer-img-tool-handler .cherry-toolbar-button:hover i,
.cherry-toolbar .cherry-sidebar .cherry-toolbar-button:hover i {
  color: var(--toolbar-btn-hover-color) !important;
}
.cherry-toolbar .cherry-sidebar {
  box-shadow: var(--shadow-md);
}

.cherry {
  --ace-serach-bg: var(--dropdown-bg);
  --ace-search-text-color: var(--toolbar-btn-color);
}
.cherry.theme__default {
  --ace-serach-bg: var(--oc-white);
  --ace-search-text-color: var(--base-font-color);
}
.cherry .ace_search {
  background: var(--ace-serach-bg);
  color: var(--ace-search-text-color);
  border: var(--border-width-1) solid var(--dropdown-border);
  border-radius: var(--radius-2xl);
  max-width: 340px;
  overflow: hidden;
  box-shadow: var(--dropdown-shadow);
  padding: var(--spacing-sm);
  position: absolute;
  top: var(--spacing-lg);
  z-index: 99;
  white-space: normal;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.cherry .ace_search.left {
  left: var(--spacing-lg);
}
.cherry .ace_search.right {
  right: var(--spacing-lg);
}
.cherry .ace_search .ace_search_form,
.cherry .ace_search .ace_replace_form {
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  border: 1.5px solid var(--oc-gray-3);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  margin-right: var(--spacing-2xl);
  display: flex;
  align-items: center;
}
.cherry .ace_search .ace_search_form:focus-within,
.cherry .ace_search .ace_replace_form:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--secondary-color);
}
.cherry .ace_search .ace_search_form.ace_nomatch,
.cherry .ace_search .ace_replace_form.ace_nomatch {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
.cherry .ace_search .ace_search_field {
  background: transparent;
  color: var(--ace-search-text-color);
  border: none;
  box-sizing: border-box;
  height: 22px;
  outline: 0;
  padding: var(--spacing-md) var(--spacing-sm);
  width: 238px;
}
.cherry .ace_search .ace_search_field::-moz-placeholder {
  color: var(--oc-gray-5);
}
.cherry .ace_search .ace_search_field::placeholder {
  color: var(--oc-gray-5);
}
.cherry .ace_search .ace_searchbtn,
.cherry .ace_search .ace_replacebtn {
  background: var(--ace-serach-bg);
  color: var(--ace-search-text-color);
  border: 0 none;
  border-left: 1px solid var(--oc-gray-3);
  cursor: pointer;
  height: 24px;
  width: 26px;
  padding: 0 var(--spacing-xs);
  margin: 0;
  position: relative;
  transition: all 0.15s ease;
}
.cherry .ace_search .ace_searchbtn:hover,
.cherry .ace_search .ace_replacebtn:hover {
  background: var(--oc-gray-3);
  color: var(--oc-gray-8);
}
.cherry .ace_search .ace_searchbtn:last-child,
.cherry .ace_search .ace_replacebtn:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}
.cherry .ace_search .ace_searchbtn:disabled {
  background: none;
  cursor: default;
}
.cherry .ace_search .ace_searchbtn_close {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--ace-search-text-color);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-lg);
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cherry .ace_search .ace_searchbtn_close:hover {
  background: var(--oc-gray-3);
  color: var(--oc-gray-8);
  transform: scale(1.1);
}
.cherry .ace_search .ace_button {
  display: block;
  border: 1px solid var(--oc-gray-3);
  color: var(--ace-search-text-color);
  cursor: pointer;
  font-weight: var(--font-weight-semibold);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: hidden;
  opacity: var(--opacity-75);
  box-sizing: border-box;
  width: var(--spacing-xl);
  height: var(--spacing-xl);
  line-height: var(--spacing-xl);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: all 0.15s ease;
}
.cherry .ace_search .ace_button:hover {
  background: var(--oc-gray-3);
  color: var(--oc-gray-8);
  transform: translateY(-1px);
  opacity: var(--opacity-100);
}
.cherry .ace_search .ace_button.checked {
  border-color: var(--primary-color);
  background-color: var(--secondary-color);
  color: var(--ace-search-text-color);
  opacity: var(--opacity-100);
}
.cherry .ace_search .ace_search_options {
  clear: both;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.cherry .ace_search .ace_search_counter {
  line-height: var(--spacing-xl);
  padding: 0 var(--spacing-sm);
}
.cherry .ace_search button svg,
.cherry .ace_search path {
  pointer-events: none;
}

.cherry {
  --seraching-highlight-color: rgba(252, 195, 25, 0.5);
}
.cherry.theme__dark {
  --seraching-highlight-color: rgba(77, 171, 247, 0.4);
}
.cherry .cm-searching {
  background-color: var(--seraching-highlight-color);
}

.cherry-sidebar {
  width: 30px;
  position: absolute;
  top: 48px;
  right: 7px;
  z-index: 11;
  bottom: 0;
  overflow: hidden;
}
.cherry-sidebar .cherry-toolbar-button {
  height: 30px;
  width: 30px;
  padding: 0 6px;
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  border-radius: var(--toolbar-btn-radius);
  transition: color 150ms ease, background-color 150ms ease, border-color 150ms ease;
}
.cherry-sidebar .cherry-toolbar-button:hover {
  background: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
}
.cherry-sidebar .cherry-toolbar-button .icon-loading.loading {
  display: inline-block;
  width: 8px;
  height: 8px;
}
.cherry-sidebar .cherry-toolbar-button .icon-loading.loading:after {
  content: " ";
  display: block;
  width: 8px;
  height: 8px;
  margin-left: 2px;
  margin-top: -2px;
  border-radius: 50%;
  border: 2px solid var(--toolbar-btn-color);
  border-color: var(--toolbar-btn-color) transparent var(--toolbar-btn-color) transparent;
  animation: loading 1.2s linear infinite;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.cherry-bubble, .cherry .cherry-previewer-img-tool-handler {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  font-size: var(--font-size-sm);
  min-height: var(--bubble-btn-height);
  min-width: 50px;
  border: var(--border-width-1) solid var(--bubble-border);
  background-color: var(--bubble-bg);
  box-shadow: var(--bubble-shadow);
  border-radius: var(--bubble-radius);
  z-index: var(--bubble-z-index);
  padding: var(--spacing-xs);
}
.cherry-bubble.cherry-bubble--centered, .cherry .cherry-bubble--centered.cherry-previewer-img-tool-handler {
  left: 50%;
  transform: translateX(-50%);
}
.cherry-bubble .cherry-bubble-top, .cherry .cherry-previewer-img-tool-handler .cherry-bubble-top,
.cherry-bubble .cherry-bubble-bottom,
.cherry .cherry-previewer-img-tool-handler .cherry-bubble-bottom {
  position: absolute;
  left: 50%;
  width: 12px;
  height: 12px;
  margin-left: -6px;
  background: var(--bubble-bg);
  box-sizing: border-box;
  z-index: calc(var(--bubble-z-index) + 1);
  pointer-events: none;
}
.cherry-bubble .cherry-bubble-top, .cherry .cherry-previewer-img-tool-handler .cherry-bubble-top {
  top: 0;
  transform: translateY(-50%) rotate(45deg);
  border-top: var(--border-width-1) solid var(--bubble-border);
  border-left: var(--border-width-1) solid var(--bubble-border);
  border-right: var(--border-width-1) solid transparent;
  border-bottom: var(--border-width-1) solid transparent;
}
.cherry-bubble .cherry-bubble-bottom, .cherry .cherry-previewer-img-tool-handler .cherry-bubble-bottom {
  bottom: 0;
  transform: translateY(50%) rotate(45deg);
  border-bottom: var(--border-width-1) solid var(--bubble-border);
  border-right: var(--border-width-1) solid var(--bubble-border);
  border-top: var(--border-width-1) solid transparent;
  border-left: var(--border-width-1) solid transparent;
}
.cherry-bubble .cherry-toolbar-button, .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: var(--bubble-btn-height);
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  padding: var(--bubble-padding);
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  z-index: calc(var(--bubble-z-index) + 2);
}
.cherry-bubble .cherry-toolbar-button:hover, .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button:hover {
  border-color: var(--toolbar-border);
  background-color: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
}
.cherry-bubble .cherry-toolbar-button.cherry-toolbar-split, .cherry .cherry-previewer-img-tool-handler .cherry-toolbar-button.cherry-toolbar-split {
  height: 65%;
  min-height: calc(0.65 * var(--bubble-btn-height));
}

.cherry-switch-paste {
  display: flex;
  align-items: center;
  justify-content: center;
}
.cherry-switch-paste .cherry-toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  text-align: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  border-radius: var(--dropdown-radius);
  border: none;
  transition: background-color 0.2s;
}
.cherry-switch-paste .cherry-toolbar-button:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-switch-paste .cherry-toolbar-button.active {
  background-color: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
}

.cherry-floatmenu {
  z-index: 4;
  display: none;
  position: absolute;
  left: 30px;
  margin-left: 60px;
  height: var(--line-height-relaxed);
  line-height: var(--line-height-relaxed);
  border-radius: var(--radius-md);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.cherry-floatmenu .cherry-toolbar-button {
  float: left;
  padding: 0 var(--spacing-sm);
  margin: 0;
  height: var(--line-height-relaxed);
  line-height: var(--line-height-relaxed);
  font-size: var(--toolbar-font-size);
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  border-radius: var(--radius-xs);
  overflow: hidden;
  vertical-align: middle;
  text-align: center;
  border: 0;
  cursor: pointer;
  font-style: normal;
  transition: color 150ms ease, background-color 150ms ease, border-color 150ms ease;
}
.cherry-floatmenu .cherry-toolbar-button.cherry-toolbar-split {
  border-left: 1px solid var(--toolbar-split-color);
  width: 0;
  padding: 0;
  overflow: hidden;
  height: 25px;
}
.cherry-floatmenu .cherry-toolbar-button .ch-icon {
  color: var(--toolbar-btn-color);
  font-size: var(--font-size-xs);
}
.cherry-floatmenu .cherry-toolbar-button:hover {
  background: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
}
.cherry-floatmenu .cherry-toolbar-button:hover .ch-icon {
  color: var(--toolbar-btn-hover-color);
}

.cherry-editor {
  position: relative;
  padding-top: 5px;
  padding-right: 5px;
  width: 50%;
  box-sizing: border-box;
  overflow: hidden;
  background-color: var(--base-editor-bg);
  z-index: 1;
}
.cherry-editor.cherry-editor--full {
  width: 100%;
  padding-right: 0;
}
.cherry-editor.cherry-editor--hidden {
  display: none;
}
.cherry-editor-writing-style--focus::before {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  top: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.0235294118), rgba(0, 0, 0, 0.2));
  pointer-events: none;
  z-index: 11;
}
.cherry-editor-writing-style--focus::after {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.0235294118), rgba(0, 0, 0, 0.2));
  pointer-events: none;
  z-index: 11;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines {
  position: relative;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines::before {
  content: "";
  display: block;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines::after {
  content: "";
  display: block;
}
.cherry-editor .CodeMirror {
  font-family: var(--font-family-sans);
  background-color: var(--base-editor-bg);
  color: var(--base-font-color);
}
.cherry-editor .CodeMirror textarea {
  font-size: var(--md-paragraph-line-height);
}
.cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid var(--editor-cursor-color);
}
.cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: var(--editor-selection-bg);
}
.cherry-editor .CodeMirror .CodeMirror-scroll span,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-string,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-em,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: var(--base-font-color);
}
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-header,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: var(--editor-header-color);
}
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: var(--editor-url-bg-color);
}
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment,
.cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: var(--editor-comment-color);
}
.cherry-editor .CodeMirror-lines {
  padding: 15px 34px;
}
.cherry-editor .CodeMirror-lines .long-text,
.cherry-editor .CodeMirror-lines .drawio,
.cherry-editor .CodeMirror-lines .base64,
.cherry-editor .CodeMirror-lines .url-truncated {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  white-space: nowrap;
  vertical-align: bottom;
  color: darkmagenta !important;
  font-size: var(--font-size-xs) !important;
}
.cherry-editor .CodeMirror-lines .url-truncated {
  max-width: 200px;
}
.cherry-editor .cm-s-default .cm-header {
  color: var(--editor-header-color);
}
.cherry-editor .cm-s-default .cm-string {
  color: var(--editor-string-color);
}
.cherry-editor .cm-s-default .cm-comment {
  color: var(--editor-comment-color);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-whitespace,
.cherry-editor .cm-tab {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-quote {
  color: var(--editor-quote-color);
}
.cherry-editor .cm-s-default .cm-link {
  color: var(--editor-link-color);
}
.cherry-editor .cm-s-default .cm-url {
  background: var(--editor-url-bg-color);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-variable-2 {
  color: var(--editor-v2-color);
}
.cherry-editor .cm-s-default .cm-variable-3 {
  color: var(--editor-v3-color);
}
.cherry-editor .cm-s-default .cm-keyword {
  color: var(--editor-keyword-color);
}
.cherry-editor .cm-s-default .cm-fullWidth {
  color: var(--full-width-color) !important;
  z-index: 3;
  cursor: pointer;
}

.cherry-drag {
  width: 15px;
  cursor: ew-resize;
  position: absolute;
  z-index: 12;
  background: transparent;
}
.cherry-drag.cherry-drag--show {
  width: 5px;
  display: block;
  background: #dfe6ee;
}
.cherry-drag.cherry-drag--hidden {
  display: none;
}

.cherry-editor-mask {
  z-index: 10;
  position: absolute;
  display: none;
  background: rgba(0, 0, 0, 0.2);
}
.cherry-editor-mask.cherry-editor-mask--show {
  display: block;
}

.cherry-previewer-mask {
  z-index: 10;
  position: absolute;
  display: none;
  background: rgba(0, 0, 0, 0.4);
}
.cherry-previewer-mask.cherry-previewer-mask--show {
  display: block;
}

.cherry-previewer-codeBlock-click-handler {
  position: absolute;
  z-index: 1;
}

.cherry-mask-code-block {
  width: 100%;
  padding-top: 50px;
  background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, var(--toolbar-bg) 100%);
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0.5em;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.7s ease-in-out, height 0.7s ease-in-out;
}
.cherry-mask-code-block .expand-btn {
  width: 25px;
  height: 25px;
  border: 1px solid rgba(255, 255, 255, 0);
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.3s;
  z-index: 12;
  color: var(--toolbar-btn-color);
  background-color: var(--toolbar-bg);
  display: inline-block;
}
.cherry-mask-code-block .expand-btn:hover {
  color: var(--toolbar-btn-hover-color);
  background-color: var(--toolbar-btn-hover-bg);
  border-color: #eee;
}

.cherry-code-unExpand pre {
  height: 240px;
  overflow: hidden !important;
}
.cherry-code-unExpand .cherry-mask-code-block {
  display: inline-block;
  opacity: 1;
  visibility: visible;
}

.cherry-previewer-codeBlock-hover-handler {
  z-index: 0;
  position: absolute;
  margin-top: -20px;
  pointer-events: none;
}
.cherry-previewer-codeBlock-hover-handler * {
  pointer-events: all;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block {
  position: relative;
  width: 25px;
  text-align: center;
  height: 25px;
  cursor: pointer;
  float: right;
  top: 35px;
  border-radius: 5px;
  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  z-index: 2;
  color: var(--base-font-color);
  background-color: var(--toolbar-bg);
  border: 1px solid var(--base-border-color);
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn {
  width: auto;
  padding: 0 5px;
}
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block {
  position: absolute;
  box-shadow: 5px 0 5px rgba(0, 0, 0, 0.05);
  width: 25px;
  text-align: center;
  height: 25px;
  cursor: pointer;
  float: right;
  border-radius: 5px;
  margin-left: -27px;
  transition: all 0.3s;
  z-index: 2;
  color: var(--toolbar-btn-color);
  background-color: var(--primary-color);
  border: 1px solid var(--base-border-color);
  top: 45px;
  right: 10px;
}
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block {
  z-index: 12;
}
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block.hidden {
  display: none;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block {
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease, color 0.2s ease;
  transform: translateY(0);
  outline: none;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block:hover {
  color: var(--toolbar-btn-hover-color);
  background-color: var(--toolbar-btn-hover-bg);
  border-color: transparent;
  transform: translateY(-2px);
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn:active,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block:active,
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block:active,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block:active,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block:active {
  color: var(--toolbar-btn-hover-color);
  transform: translateY(0);
  box-shadow: none;
  background-color: var(--toolbar-btn-active-bg);
  transition-duration: 0.1s;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn:focus-visible,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block:focus-visible,
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block:focus-visible,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block:focus-visible,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block:focus-visible {
  box-shadow: 0 0 0 2px var(--base-previewer-bg), 0 0 0 4px var(--primary-color);
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select {
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  font-size: 14px;
  color: var(--base-font-color);
  background-color: var(--toolbar-bg);
  border: 1px solid var(--base-border-color);
  border-radius: 6px;
  padding: 5px 36px 5px 12px;
  cursor: pointer;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 12px 12px;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 2px var(--base-previewer-bg), 0 0 0 4px var(--primary-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 11 6-6 6 6'/%3e%3c/svg%3e");
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select option {
  color: var(--base-font-color);
  font-size: 14px;
  background-color: var(--toolbar-bg);
  cursor: cursor;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select option:hover {
  background-color: var(--toolbar-btn-hover-bg);
  color: var(--toolbar-btn-hover-color);
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select:disabled {
  background-color: var(--base-border-color);
  color: var(--base-font-color);
  cursor: not-allowed;
}

.float-previewer-wrap {
  position: fixed;
  right: 0;
  top: 0;
  z-index: 100;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.1);
  resize: both;
  min-width: 430px;
  min-height: 300px;
}
.float-previewer-wrap.float-previewer-dragging {
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.3);
}
.float-previewer-wrap.float-previewer-dragging .float-previewer-header {
  cursor: grabbing;
  background: #ace4ff;
}
.float-previewer-wrap .float-previewer-header {
  z-index: 999999;
  height: 40px;
  border-bottom: 1px solid #ebedee;
  background: #caecfd;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  cursor: grab;
}
.float-previewer-wrap .float-previewer-header .float-previewer-title {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: var(--font-size-md);
  color: #333;
  font-weight: bold;
}
.float-previewer-wrap .cherry-previewer {
  border-left: none;
}

.cherry-previewer {
  padding: 20px 45px 20px 20px;
  border-left: 2px solid var(--drag-border-color);
  width: 50%;
  box-sizing: border-box;
  background-color: var(--base-previewer-bg);
  min-height: auto;
  overflow-y: auto;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}
.cherry-previewer .cherry-mobile-previewer-content {
  width: 375px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  overflow-y: scroll;
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  background-color: var(--previewer-mobile-bg);
}
.cherry-previewer.cherry-previewer--hidden {
  width: 0;
  display: none;
}
.cherry-previewer.cherry-previewer--full {
  width: 100%;
}
.cherry-previewer .cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-previewer .cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-previewer .cherry-list__lower-alpha {
  list-style: lower-alpha;
}
.cherry-previewer .cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-previewer .cherry-list__circle {
  list-style: circle;
}
.cherry-previewer .cherry-list__square {
  list-style: square;
}
[data-code-block-theme=default] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=default] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=funky] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=funky] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=solarized-light] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=coy] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=coy] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-edit-code-block {
  background-color: #3582fb;
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.cherry-previewer .cherry-flow-session-cursor {
  background-color: rgba(53, 130, 251, 0.5333333333);
  padding: 0 2.5px;
  animation: blink 1s infinite;
}

.cherry-color-wrap {
  display: none;
  position: fixed;
  width: 300px;
  padding: 16px;
  z-index: 13;
  background: var(--toolbar-bg);
  box-shadow: var(--shadow-md);
  border-radius: var(--dropdown-radius);
  border: 1px solid var(--dropdown-border);
}
.cherry-color-wrap .cherry-color-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-tab-group {
  display: flex;
  background: var(--border-color);
  border-radius: var(--radius-lg);
  padding: 2px;
  width: auto;
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-tab {
  padding: 8px 12px;
  text-align: center;
  font-size: 12px;
  color: var(--toolbar-btn-color);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all 0.2s;
  white-space: nowrap;
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-tab.active {
  background: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-tab:hover:not(.active) {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-clear {
  font-size: 12px;
  color: var(--toolbar-btn-color);
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--radius-lg);
  transition: all 0.2s;
  white-space: nowrap;
}
.cherry-color-wrap .cherry-color-tabs .cherry-color-clear:hover {
  background-color: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-color-wrap .cherry-color-picker {
  margin-bottom: 16px;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-main {
  width: 100%;
  height: 160px;
  border-radius: 6px;
  position: relative;
  cursor: crosshair;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-main .cherry-color-saturation {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: crosshair;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-main .cherry-color-saturation .cherry-color-pointer {
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 2;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-hue-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-hue {
  flex: 1;
  height: 14px;
  border-radius: 7px;
  background: linear-gradient(to right, #ff0000 7px, #ffff00 calc(17% + 7px), #00ff00 calc(33% + 7px), #00ffff calc(50% + 7px), #0000ff calc(67% + 7px), #ff00ff calc(83% + 7px), #ff0000 calc(100% - 7px));
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-hue .cherry-color-hue-pointer {
  position: absolute;
  top: 50%;
  width: 14px;
  height: 14px;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  z-index: 2;
}
.cherry-color-wrap .cherry-color-picker .cherry-color-preview {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}
.cherry-color-wrap .cherry-color-recent .cherry-color-section-title,
.cherry-color-wrap .cherry-color-presets .cherry-color-section-title {
  font-size: 12px;
  color: var(--toolbar-btn-color);
  margin-bottom: 8px;
  font-weight: 500;
}
.cherry-color-wrap .cherry-color-recent {
  margin-bottom: 16px;
}
.cherry-color-wrap .cherry-color-recent .cherry-color-recent-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.cherry-color-wrap .cherry-color-recent .cherry-color-recent-grid .cherry-color-recent-empty {
  background: var(--bg-color-lighter);
  border-style: dashed;
  cursor: default;
}
.cherry-color-wrap .cherry-color-recent .cherry-color-recent-grid .cherry-color-recent-empty:hover {
  transform: none;
}
.cherry-color-wrap .cherry-color-presets .cherry-color-preset-grid {
  display: flex;
  flex-wrap: wrap;
}
.cherry-color-wrap .cherry-color-presets .cherry-color-preset-grid .cherry-color-preset-row {
  display: flex;
  gap: 6px;
  margin-bottom: 6px;
  width: 100%;
}
.cherry-color-wrap .cherry-color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.1s;
  border: 1px solid var(--dropdown-border);
}
.cherry-color-wrap .cherry-color-item:hover {
  transform: scale(1.15);
  z-index: 1;
  position: relative;
}

.Cherry-Math svg {
  max-width: 100%;
}

.cherry-suggester-panel {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  border-radius: var(--dropdown-radius);
  box-shadow: var(--dropdown-shadow);
  padding: var(--dropdown-padding);
  max-height: 500px;
  z-index: var(--z-index-dropdown, 13);
  overflow-x: hidden;
  overflow-y: auto;
}
.cherry-suggester-panel .cherry-suggester-panel__item {
  border: none;
  white-space: nowrap;
  min-width: 50px;
  padding: var(--dropdown-item-padding);
  color: var(--toolbar-btn-color);
  background: var(--toolbar-btn-bg);
  display: block;
  cursor: pointer;
  border-radius: var(--dropdown-item-radius);
  font-size: var(--font-size-sm);
  margin: 2px 4px;
  transition: color 150ms ease, background-color 150ms ease, border-color 150ms ease;
  display: flex;
  align-items: center;
}
.cherry-suggester-panel .cherry-suggester-panel__item:hover {
  background: var(--dropdown-item-hover-bg);
  color: var(--dropdown-item-hover-color);
}
.cherry-suggester-panel .cherry-suggester-panel__item.cherry-suggester-panel__item--selected {
  background: var(--dropdown-item-active-bg);
  color: var(--dropdown-item-active-color);
  text-decoration: none;
}
.cherry-suggester-panel .cherry-suggester-panel__item > i {
  display: inline-block;
  transform: translateY(2px);
  margin-right: 8px;
}
.cherry-suggester-panel .cherry-suggester-panel__item .ch-icon {
  margin-right: 8px;
  vertical-align: middle;
  font-size: var(--font-size-md);
  display: inline-block;
  line-height: 1;
  position: static;
}

.cherry-suggestion {
  background-color: #ebf3ff;
  color: #3582fb;
  padding: 1px 4px;
  border-radius: 3px;
  cursor: pointer;
}

.cherry-flex-toc {
  z-index: 11;
  position: absolute;
  width: 160px;
  height: calc(100% - 220px);
  max-height: 600px;
  right: 0;
  top: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: rgba(255, 255, 255, 0.8666666667);
  margin-right: 8px;
  box-sizing: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: 0px 5px 11px rgba(51, 51, 51, 0.2);
  border-radius: 10px;
  transition: all 0.3s;
}
.cherry-flex-toc.cherry-flex-toc__fixed {
  position: fixed;
}
.cherry-flex-toc:hover {
  background-color: #fff;
  width: 260px;
}
.cherry-flex-toc .cherry-toc-head {
  border-bottom: 1px dashed rgba(51, 51, 51, 0.2);
  padding: 5px;
}
.cherry-flex-toc .cherry-toc-head .cherry-toc-title {
  font-size: var(--font-size-md);
  font-weight: bold;
  color: var(--primary-color);
  padding-left: 5px;
}
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsLeft {
  display: none;
}
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsRight,
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsLeft {
  padding: 5px;
  position: absolute;
  right: 0;
  top: 0;
}
.cherry-flex-toc .cherry-toc-head i {
  cursor: pointer;
  padding: 5px 5px 0;
}
.cherry-flex-toc .cherry-toc-head i:hover {
  color: #3582fb;
}
.cherry-flex-toc .cherry-toc-list {
  overflow-y: auto;
  height: calc(100% - 40px);
  overflow-x: hidden;
  width: 100%;
  padding-bottom: 10px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a {
  display: block;
  text-decoration: none;
  color: #000;
  border-left: 5px solid rgba(51, 51, 51, 0.2);
  height: 28px;
  line-height: 28px;
  transition: all 0.3s;
  padding-left: 10px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  cursor: pointer;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a:hover, .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a.current {
  border-left-color: var(--primary-color);
  color: var(--primary-color);
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__1 {
  font-weight: bold;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__2 {
  padding-left: 20px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__3 {
  padding-left: 40px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__4 {
  padding-left: 60px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__5 {
  padding-left: 80px;
}
.cherry-flex-toc.cherry-flex-toc__pure {
  width: 30px;
  height: calc(100% - 200px);
  max-height: 600px;
  background: rgba(255, 255, 255, 0);
  box-shadow: none;
  border-radius: 0;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head {
  height: 25px;
  border-bottom: 1px dashed rgba(51, 51, 51, 0);
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .cherry-toc-title {
  display: none;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsRight {
  display: none;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsLeft {
  display: inline;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list {
  padding-left: 7px;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list .cherry-toc-one-a {
  overflow: hidden;
  width: 0;
  margin-bottom: 3px;
  height: 5px;
  border-left-width: 18px;
}
.cherry-flex-toc.auto-num .cherry-toc-list {
  counter-reset: toclevel1;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1 {
  counter-reset: toclevel2;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2 {
  counter-reset: toclevel3;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3 {
  counter-reset: toclevel4;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4 {
  counter-reset: toclevel5;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5 {
  counter-reset: toclevel6;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1:before {
  counter-increment: toclevel1;
  content: counter(toclevel1) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2:before {
  counter-increment: toclevel2;
  content: counter(toclevel1) "." counter(toclevel2) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3:before {
  counter-increment: toclevel3;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4:before {
  counter-increment: toclevel4;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__6:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) "." counter(toclevel6) ". ";
}

/** 引入自带的主题 */
/*
 * 默认主题
 */
.cherry.theme__default {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: var(--oc-blue-6);
  --secondary-color: var(--oc-blue-0);
  --base-font-color: var(--oc-gray-8);
  --base-editor-bg: var(--oc-white);
  --base-previewer-bg: var(--oc-white);
  --base-border-color: var(--oc-blue-5);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-white);
  --toolbar-btn-color: var(--oc-gray-8);
  --toolbar-btn-hover-bg: var(--secondary-color);
  --toolbar-btn-hover-color: var(--primary-color);
  --toolbar-btn-active-bg: var(--secondary-color);
  --toolbar-split-color: var(--oc-blue-5);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--primary-color);
  --editor-string-color: var(--oc-blue-4);
  --editor-comment-color: var(--oc-blue-4);
  --editor-quote-color: var(--oc-gray-8);
  --editor-link-color: var(--oc-blue-4);
  --editor-url-bg-color: var(--oc-blue-0);
  --editor-v2-color: var(--oc-gray-8);
  --editor-v3-color: var(--primary-color);
  --editor-keyword-color: var(--oc-blue-4);
  --editor-selection-bg: var(--oc-blue-2);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--primary-color);
  --md-paragraph-color: var(--oc-gray-8);
  --md-link-color: var(--oc-blue-4);
  --md-link-hover-color: var(--primary-color);
  --md-inline-code-color: var(--oc-blue-4);
  --md-inline-code-bg: var(--oc-blue-0);
  --md-blockquote-bg: var(--oc-blue-0);
  --md-hr-border: var(--oc-blue-5);
  --md-table-border: var(--oc-blue-5);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-blue-0);
  --accordion-border: var(--oc-blue-2);
  --accordion-shadow: var(--shadow-sm);
  --accordion-summary-bg: var(--oc-blue-5);
  --accordion-summary-hover-bg: var(--oc-blue-4);
  --accordion-body-bg: var(--oc-blue-0);
  --accordion-body-border: var(--oc-blue-1);
  --accordion-body-color: var(--oc-gray-8);
}

/** 预览区域样式 */
.cherry-markdown.theme__default {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
  /** 行内代码 */
  /** 
   * 代码块
   */
  /** 
   * 表格
   */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__default h1,
.cherry-markdown.theme__default h2,
.cherry-markdown.theme__default h3,
.cherry-markdown.theme__default h4,
.cherry-markdown.theme__default h5,
.cherry-markdown.theme__default h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__default ruby {
  /** 上部的拼音 */
}
/*
 * 暗黑主题
 */
.cherry.theme__dark {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: var(--oc-orange-5);
  --secondary-color: #513838;
  --base-font-color: var(--oc-gray-4);
  --base-editor-bg: var(--oc-gray-9);
  --base-previewer-bg: var(--oc-gray-8);
  --base-border-color: var(--oc-gray-5);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-gray-7);
  --toolbar-btn-color: var(--oc-gray-4);
  --toolbar-btn-hover-bg: var(--oc-gray-6);
  --toolbar-btn-hover-color: var(--oc-white);
  --toolbar-btn-active-bg:var(--oc-gray-8);
  --toolbar-split-color: var(--oc-gray-5);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--primary-color);
  --editor-string-color: var(--oc-yellow-3);
  --editor-comment-color: var(--oc-yellow-3);
  --editor-quote-color: var(--oc-gray-4);
  --editor-link-color: var(--oc-yellow-3);
  --editor-url-bg-color: rgb(81, 56, 56);
  --editor-v2-color: var(--oc-gray-4);
  --editor-v3-color: var(--primary-color);
  --editor-keyword-color: var(--oc-yellow-3);
  --editor-selection-bg: rgba(134, 142, 150, 0.8);
  --dropdown-item-hover-bg: var(--oc-gray-9);
  --dropdown-item-hover-color: var(--oc-orange-5);
  --dropdown-item-active-bg: var(--secondary-color);
  --dropdown-item-active-color: var(--primary-color);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--oc-orange-5);
  --md-paragraph-color: var(--oc-gray-4);
  --md-link-color: var(--oc-yellow-3);
  --md-link-hover-color: var(--oc-orange-5);
  --md-inline-code-color: var(--oc-yellow-3);
  --md-inline-code-bg: rgb(81, 56, 56);
  --md-blockquote-bg: rgba(102, 128, 153, 0.05);
  --md-hr-border: var(--oc-gray-5);
  --md-table-border: var(--oc-gray-5);
  --md-toc-bg: var(--oc-gray-9);
  --md-toc-border-color: var(--oc-gray-7);
  --md-toc-indicator-color: var(--oc-gray-7);
  --md-toc-link-hover-bg: var(--oc-gray-8);
  --md-toc-link-active-bg: var(--oc-gray-7);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-gray-8);
  --accordion-border: var(--oc-gray-6);
  --accordion-shadow: var(--shadow-md);
  --accordion-summary-bg: var(--oc-orange-6);
  --accordion-summary-hover-bg: var(--oc-orange-5);
  --accordion-body-bg: var(--oc-gray-7);
  --accordion-body-border: var(--oc-gray-6);
  --accordion-body-color: var(--oc-gray-4);
  /* 目录区域样式 */
}
.cherry.theme__dark .cherry-flex-toc:hover {
  background-color: var(--oc-gray-4);
  width: 260px;
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-head i:hover {
  color: var(--primary-color);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a {
  border-left-color: rgba(255, 146, 43, 0.3);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a.current {
  border-left-color: var(--oc-orange-6);
  color: var(--oc-orange-6);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a:hover {
  border-left-color: var(--oc-orange-4);
  color: var(--oc-orange-6);
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure {
  width: 30px;
  height: calc(100% - 200px);
  max-height: 600px;
  background: transparent;
  box-shadow: none;
  border-radius: 0;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head {
  height: 25px;
  border-bottom: 1px dashed transparent;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .cherry-toc-title {
  display: none;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsRight {
  display: none;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsLeft {
  display: inline;
  color: var(--oc-orange-6);
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list {
  padding-left: 7px;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list .cherry-toc-one-a {
  overflow: hidden;
  width: 0;
  margin-bottom: 3px;
  height: 5px;
  border-left-width: 18px;
}

/** 快捷键配置面板样式 */
.cherry-shortcut-key-config-panel-wrapper .shortcut-panel-title, .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings {
  color: var(--oc-gray-9);
}

/** 预览区域样式 */
.cherry-markdown.theme__dark {
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 16px;
  --panel-bg: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
  --panel-border: 1px solid rgba(109, 40, 217, 0.2);
  --panel-box-shadow: none;
  --panel-backdrop-filter: blur(10px);
  --panel-title-bg: linear-gradient(145deg, #333333 0%, #2a2a2a 100%);
  --panel-title-color: var(--oc-gray-1);
  --panel-title-border-radius: 16px 16px 0 0;
  --panel-title-border-bottom: 1px solid rgba(109, 40, 217, 0.15);
  --panel-body-bg: linear-gradient(145deg, #242424 0%, #1e1e1e 100%);
  --panel-body-color: var(--oc-gray-2);
  --panel-body-border-radius: 0 0 16px 16px;
  /* Primary Panel */
  --panel-primary-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.08) 0%, rgba(67, 56, 202, 0.08) 100%);
  --panel-primary-border-color: rgba(109, 40, 217, 0.3);
  --panel-primary-box-shadow: none;
  --panel-primary-title-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.3) 0%, rgba(67, 56, 202, 0.3) 100%);
  --panel-primary-title-color: var(--oc-violet-2);
  --panel-primary-body-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.05) 0%, rgba(67, 56, 202, 0.05) 100%);
  --panel-primary-body-color: var(--oc-violet-3);
  /* Info Panel */
  --panel-info-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.08) 0%, rgba(59, 130, 246, 0.08) 100%);
  --panel-info-border-color: rgba(34, 184, 207, 0.3);
  --panel-info-box-shadow: none;
  --panel-info-title-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%);
  --panel-info-title-color: var(--oc-cyan-2);
  --panel-info-body-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  --panel-info-body-color: var(--oc-cyan-3);
  /* Warning Panel */
  --panel-warning-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.08) 0%, rgba(251, 191, 36, 0.08) 100%);
  --panel-warning-border-color: rgba(255, 193, 7, 0.3);
  --panel-warning-box-shadow: none;
  --panel-warning-title-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.3) 0%, rgba(251, 191, 36, 0.3) 100%);
  --panel-warning-title-color: var(--oc-yellow-2);
  --panel-warning-body-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.05) 0%, rgba(251, 191, 36, 0.05) 100%);
  --panel-warning-body-color: var(--oc-yellow-3);
  /* Danger Panel */
  --panel-danger-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.08) 0%, rgba(220, 38, 127, 0.08) 100%);
  --panel-danger-border-color: rgba(239, 68, 68, 0.3);
  --panel-danger-box-shadow: none;
  --panel-danger-title-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.3) 100%);
  --panel-danger-title-color: var(--oc-red-2);
  --panel-danger-body-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 127, 0.05) 100%);
  --panel-danger-body-color: var(--oc-red-3);
  /* Success Panel */
  --panel-success-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.08) 0%, rgba(16, 185, 129, 0.08) 100%);
  --panel-success-border-color: rgba(34, 197, 94, 0.3);
  --panel-success-box-shadow: none;
  --panel-success-title-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.3) 100%);
  --panel-success-title-color: var(--oc-green-2);
  --panel-success-body-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  --panel-success-body-color: var(--oc-green-3);
}
.cherry-markdown.theme__dark figure svg path,
.cherry-markdown.theme__dark figure svg rect,
.cherry-markdown.theme__dark figure svg line {
  stroke: var(--oc-yellow-3) !important;
}
.cherry-markdown.theme__dark figure svg text {
  fill: var(--oc-yellow-6) !important;
  stroke: none !important;
}
.cherry-markdown.theme__dark figure svg tspan {
  fill: var(--oc-yellow-6) !important;
}
.cherry-markdown.theme__dark figure svg circle {
  fill: var(--oc-violet-0) !important;
}
.cherry-markdown.theme__dark figure svg circle.state-start {
  fill: var(--oc-yellow-6) !important;
}
@keyframes changeBgColorDark {
  0% {
    background-color: var(--oc-gray-6);
  }
  60% {
    background-color: var(--oc-gray-6);
  }
  100% {
    background-color: var(--base-previewer-bg);
  }
}

/*
 * 深海主题
 */
.cherry.theme__abyss {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: #8394AD;
  --secondary-color: #2C2E37;
  --base-font-color: #CECFCF;
  --base-editor-bg: #1E222A;
  --base-previewer-bg: #1E222A;
  --base-border-color: var(--primary-color);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: #181C24;
  --toolbar-btn-color: #CECFCF;
  --toolbar-btn-hover-bg: var(--secondary-color);
  --toolbar-btn-hover-color: var(--primary-color);
  --toolbar-btn-active-bg: var(--secondary-color);
  --toolbar-split-color: var(--oc-blue-5);
  --toolbar-shadow: none;
  /* ========== 工具栏的下拉菜单 ========== */
  --dropdown-bg: var(--toolbar-bg);
  --dropdown-border: var(--base-border-color);
  --dropdown-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --dropdown-radius: var(--radius-xl);
  --dropdown-padding: var(--spacing-xs) 0;
  --dropdown-item-hover-bg: var(--toolbar-btn-hover-bg);
  --dropdown-item-color: var(--toolbar-btn-color);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--primary-color);
  --editor-string-color: var(--oc-blue-4);
  --editor-comment-color: #A8A8A9;
  --editor-quote-color: var(--oc-gray-8);
  --editor-link-color: var(--oc-blue-4);
  --editor-url-bg-color: none;
  --editor-v2-color: var(--oc-gray-8);
  --editor-v3-color: var(--primary-color);
  --editor-keyword-color: var(--oc-blue-4);
  --editor-selection-bg: #636e87;
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--primary-color);
  --md-paragraph-color: var(--base-font-color);
  --md-link-color: var(--oc-blue-4);
  --md-link-hover-color: var(--primary-color);
  --md-inline-code-color: var(--primary-color);
  --md-inline-code-bg: #00203d;
  --md-blockquote-bg: #2A2F3B;
  --md-hr-border: var(--primary-color);
  --md-table-border: var(--oc-blue-5);
  --md-toc-bg: #2A2F3B;
  --md-toc-title-color: var(--primary-color);
  --md-toc-link-color: var(--primary-color);
  --md-toc-indicator-color: var(--primary-color);
  --md-toc-link-hover-color: var(--secondary-color);
  --md-toc-link-hover-bg: var(--primary-color);
  --md-toc-indicator-hover-color: var(--primary-color);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: #2A2F3B;
  --accordion-border: var(--oc-blue-5);
  --accordion-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --accordion-summary-bg: var(--secondary-color);
  --accordion-summary-hover-bg: var(--toolbar-btn-hover-bg);
  --accordion-body-bg: #2A2F3B;
  --accordion-body-border: var(--oc-blue-5);
  --accordion-body-color: var(--base-font-color);
  /* ========== 其余单个组件 ========== */
  --drag-border-color: #3a4453;
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 16px;
  --panel-bg: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
  --panel-border: 1px solid rgba(109, 40, 217, 0.2);
  --panel-box-shadow: none;
  --panel-backdrop-filter: blur(10px);
  --panel-title-bg: linear-gradient(145deg, #333333 0%, #2a2a2a 100%);
  --panel-title-color: var(--oc-gray-1);
  --panel-title-border-radius: 16px 16px 0 0;
  --panel-title-border-bottom: 1px solid rgba(109, 40, 217, 0.15);
  --panel-body-bg: linear-gradient(145deg, #242424 0%, #1e1e1e 100%);
  --panel-body-color: var(--oc-gray-2);
  --panel-body-border-radius: 0 0 16px 16px;
  /* Primary Panel */
  --panel-primary-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.08) 0%, rgba(67, 56, 202, 0.08) 100%);
  --panel-primary-border-color: rgba(109, 40, 217, 0.3);
  --panel-primary-box-shadow: none;
  --panel-primary-title-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.3) 0%, rgba(67, 56, 202, 0.3) 100%);
  --panel-primary-title-color: var(--oc-violet-2);
  --panel-primary-body-bg: linear-gradient(145deg, rgba(109, 40, 217, 0.05) 0%, rgba(67, 56, 202, 0.05) 100%);
  --panel-primary-body-color: var(--oc-violet-3);
  /* Info Panel */
  --panel-info-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.08) 0%, rgba(59, 130, 246, 0.08) 100%);
  --panel-info-border-color: rgba(34, 184, 207, 0.3);
  --panel-info-box-shadow: none;
  --panel-info-title-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.3) 0%, rgba(59, 130, 246, 0.3) 100%);
  --panel-info-title-color: var(--oc-cyan-2);
  --panel-info-body-bg: linear-gradient(145deg, rgba(34, 184, 207, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  --panel-info-body-color: var(--oc-cyan-3);
  /* Warning Panel */
  --panel-warning-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.08) 0%, rgba(251, 191, 36, 0.08) 100%);
  --panel-warning-border-color: rgba(255, 193, 7, 0.3);
  --panel-warning-box-shadow: none;
  --panel-warning-title-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.3) 0%, rgba(251, 191, 36, 0.3) 100%);
  --panel-warning-title-color: var(--oc-yellow-2);
  --panel-warning-body-bg: linear-gradient(145deg, rgba(255, 193, 7, 0.05) 0%, rgba(251, 191, 36, 0.05) 100%);
  --panel-warning-body-color: var(--oc-yellow-3);
  /* Danger Panel */
  --panel-danger-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.08) 0%, rgba(220, 38, 127, 0.08) 100%);
  --panel-danger-border-color: rgba(239, 68, 68, 0.3);
  --panel-danger-box-shadow: none;
  --panel-danger-title-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.3) 100%);
  --panel-danger-title-color: var(--oc-red-2);
  --panel-danger-body-bg: linear-gradient(145deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 127, 0.05) 100%);
  --panel-danger-body-color: var(--oc-red-3);
  /* Success Panel */
  --panel-success-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.08) 0%, rgba(16, 185, 129, 0.08) 100%);
  --panel-success-border-color: rgba(34, 197, 94, 0.3);
  --panel-success-box-shadow: none;
  --panel-success-title-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.3) 100%);
  --panel-success-title-color: var(--oc-green-2);
  --panel-success-body-bg: linear-gradient(145deg, rgba(34, 197, 94, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  --panel-success-body-color: var(--oc-green-3);
}

.cherry-markdown.theme__abyss figure svg path,
.cherry-markdown.theme__abyss figure svg rect,
.cherry-markdown.theme__abyss figure svg line {
  stroke: var(--primary-color) !important;
}
.cherry-markdown.theme__abyss figure svg text {
  fill: var(--primary-color) !important;
  stroke: none !important;
}
.cherry-markdown.theme__abyss figure svg tspan {
  fill: var(--primary-color) !important;
}
.cherry-markdown.theme__abyss figure svg circle {
  fill: var(--oc-violet-0) !important;
}
.cherry-markdown.theme__abyss figure svg circle.state-start {
  fill: var(--oc-white) !important;
}
@keyframes changeBgColor {
  0% {
    background-color: rgba(204, 221, 255, 0.3137254902);
  }
  60% {
    background-color: rgba(204, 221, 255, 0.3137254902);
  }
  100% {
    background-color: var(--base-previewer-bg);
  }
}

/*
 * 清新主题
 */
.cherry.theme__green {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: var(--oc-green-9);
  --secondary-color: var(--oc-green-0);
  --base-font-color: var(--primary-color);
  --base-editor-bg: var(--oc-white);
  --base-previewer-bg: var(--secondary-color);
  --base-border-color: var(--oc-green-8);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-white);
  --toolbar-btn-color: var(--primary-color);
  --toolbar-btn-hover-bg: var(--oc-green-5);
  --toolbar-btn-hover-color: var(--secondary-color);
  --toolbar-btn-active-bg:var(--oc-green-7);
  --toolbar-split-color: var(--oc-green-8);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--oc-green-7);
  --editor-string-color: var(--oc-green-6);
  --editor-comment-color: var(--oc-green-6);
  --editor-quote-color: var(--primary-color);
  --editor-link-color: var(--oc-green-6);
  --editor-url-bg-color: var(--secondary-color);
  --editor-v2-color: var(--primary-color);
  --editor-v3-color: var(--oc-green-7);
  --editor-keyword-color: var(--oc-green-6);
  --editor-selection-bg: var(--oc-green-2);
  /* ========== 预览器相关变量 ========== */
  --previewer-mobile-bg: var(--oc-white);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--oc-green-7);
  --md-paragraph-color: var(--primary-color);
  --md-link-color: var(--oc-green-6);
  --md-link-hover-color: var(--oc-green-7);
  --md-inline-code-color: var(--oc-green-6);
  --md-inline-code-bg: var(--oc-green-1);
  --md-blockquote-bg: var(--oc-green-1);
  --md-hr-border: var(--oc-green-8);
  --md-table-border: var(--oc-green-8);
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 12px;
  --panel-bg: var(--oc-green-0);
  --panel-border: 1px solid var(--oc-green-2);
  --panel-box-shadow: 0 3px 12px rgba(64, 192, 87, 0.15);
  --panel-title-bg: var(--oc-green-1);
  --panel-title-color: var(--oc-green-9);
  --panel-title-border-radius: 10px 10px 0 0;
  --panel-title-border-bottom: 1px solid var(--oc-green-3);
  --panel-body-bg: var(--oc-white);
  --panel-body-color: var(--oc-green-9);
  --panel-body-border-radius: 0 0 10px 10px;
  /* Primary Panel */
  --panel-primary-border-color: var(--oc-green-4);
  --panel-primary-box-shadow: 0 3px 12px rgba(64, 192, 87, 0.25);
  --panel-primary-title-bg: var(--oc-green-3);
  --panel-primary-title-color: var(--oc-green-9);
  --panel-primary-body-bg: var(--oc-green-0);
  --panel-primary-body-color: var(--oc-green-8);
  /* Info Panel */
  --panel-info-border-color: var(--oc-cyan-4);
  --panel-info-box-shadow: 0 3px 12px rgba(9, 196, 244, 0.15);
  --panel-info-title-bg: var(--oc-cyan-2);
  --panel-info-title-color: var(--oc-cyan-9);
  --panel-info-body-bg: var(--oc-cyan-0);
  --panel-info-body-color: var(--oc-cyan-8);
  /* Warning Panel */
  --panel-warning-border-color: var(--oc-lime-4);
  --panel-warning-box-shadow: 0 3px 12px rgba(130, 201, 30, 0.15);
  --panel-warning-title-bg: var(--oc-lime-2);
  --panel-warning-title-color: var(--oc-lime-9);
  --panel-warning-body-bg: var(--oc-lime-0);
  --panel-warning-body-color: var(--oc-lime-8);
  /* Danger Panel */
  --panel-danger-border-color: var(--oc-red-4);
  --panel-danger-box-shadow: 0 3px 12px rgba(250, 82, 82, 0.15);
  --panel-danger-title-bg: var(--oc-red-2);
  --panel-danger-title-color: var(--oc-red-9);
  --panel-danger-body-bg: var(--oc-red-0);
  --panel-danger-body-color: var(--oc-red-8);
  /* Success Panel */
  --panel-success-border-color: var(--oc-green-5);
  --panel-success-box-shadow: 0 3px 12px rgba(64, 192, 87, 0.3);
  --panel-success-title-bg: var(--oc-green-4);
  --panel-success-title-color: var(--oc-white);
  --panel-success-body-bg: var(--oc-green-0);
  --panel-success-body-color: var(--oc-green-8);
  --md-toc-bg: var(--oc-white);
  --md-toc-indicator-color: var(--oc-green-1);
  --md-toc-link-hover-bg: var(--oc-green-0);
  --md-toc-link-active-bg: var(--oc-green-1);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-green-0);
  --accordion-border: var(--oc-green-2);
  --accordion-shadow: var(--shadow-sm);
  --accordion-summary-bg: var(--oc-green-6);
  --accordion-summary-hover-bg: var(--oc-green-5);
  --accordion-body-bg: var(--oc-green-0);
  --accordion-body-border: var(--oc-green-1);
  --accordion-body-color: var(--primary-color);
}

/** 预览区域样式 */
.cherry-markdown.theme__green h1,
.cherry-markdown.theme__green h2,
.cherry-markdown.theme__green h3,
.cherry-markdown.theme__green h4,
.cherry-markdown.theme__green h5 {
  text-align: center;
  margin-bottom: 35px;
}

/*
 * 热情主题
 */
.cherry.theme__red {
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-pink-0);
  --accordion-border: var(--oc-pink-2);
  --accordion-shadow: 0 4px 12px rgba(244, 63, 94, 0.08);
  --accordion-summary-bg: linear-gradient(135deg, var(--oc-pink-6), var(--oc-pink-7));
  --accordion-summary-hover-bg: linear-gradient(135deg, var(--oc-pink-5), var(--oc-pink-6));
  --accordion-accent-color: linear-gradient(to bottom, var(--oc-pink-4), var(--oc-pink-6));
  --accordion-body-bg: var(--oc-pink-0);
  --accordion-body-border: var(--oc-pink-1);
  --accordion-body-color: var(--oc-pink-9);
  --accordion-multiple-border: var(--oc-pink-2);
  --accordion-multiple-item-border: var(--oc-pink-1);
  --primary-color: var(--oc-pink-7);
  --secondary-color: var(--oc-pink-0);
  --base-font-color: var(--oc-pink-9);
  --base-editor-bg: var(--oc-pink-0);
  --base-previewer-bg: var(--oc-pink-0);
  --base-border-color: var(--oc-pink-8);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-pink-1);
  --toolbar-btn-color: var(--oc-pink-8);
  --toolbar-btn-hover-bg: var(--oc-pink-5);
  --toolbar-btn-hover-color: var(--oc-pink-0);
  --toolbar-btn-active-bg: var(--oc-pink-7);
  --toolbar-split-color: var(--oc-pink-8);
  --dropdown-bg: var(--oc-white);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--primary-color);
  --editor-string-color: var(--oc-pink-5);
  --editor-comment-color: var(--oc-pink-5);
  --editor-quote-color: var(--oc-pink-9);
  --editor-link-color: var(--oc-pink-5);
  --editor-url-bg-color: var(--oc-pink-1);
  --editor-v2-color: var(--oc-pink-9);
  --editor-v3-color: var(--primary-color);
  --editor-keyword-color: var(--oc-pink-5);
  --editor-selection-bg: var(--oc-pink-2);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--primary-color);
  --md-paragraph-color: var(--oc-pink-9);
  --md-link-color: var(--oc-pink-5);
  --md-link-hover-color: var(--primary-color);
  --md-inline-code-color: var(--oc-pink-5);
  --md-inline-code-bg: var(--oc-pink-1);
  --md-blockquote-bg: var(--oc-pink-1);
  --md-hr-border: var(--oc-pink-8);
  --md-table-border: var(--oc-pink-8);
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 6px;
  --panel-bg: linear-gradient(135deg, var(--oc-pink-0), var(--oc-pink-1));
  --panel-border: 1px solid var(--oc-pink-3);
  --panel-box-shadow: 0 2px 8px rgba(230, 28, 132, 0.15);
  --panel-title-bg: linear-gradient(135deg, var(--oc-pink-2), var(--oc-pink-1));
  --panel-title-color: var(--oc-pink-9);
  --panel-title-border-radius: 6px 6px 0 0;
  --panel-title-border-bottom: 1px solid var(--oc-pink-3);
  --panel-body-bg: var(--oc-pink-0);
  --panel-body-color: var(--oc-pink-9);
  --panel-body-border-radius: 0 0 6px 6px;
  /* Primary Panel */
  --panel-primary-bg: linear-gradient(135deg, var(--oc-pink-0), var(--oc-pink-2));
  --panel-primary-border-color: var(--oc-pink-5);
  --panel-primary-title-bg: linear-gradient(135deg, var(--oc-pink-5), var(--oc-pink-4));
  --panel-primary-title-color: var(--oc-white);
  --panel-primary-body-bg: var(--oc-pink-0);
  --panel-primary-body-color: var(--oc-pink-8);
  /* Info Panel */
  --panel-info-bg: linear-gradient(135deg, var(--oc-grape-0), var(--oc-grape-1));
  --panel-info-border-color: var(--oc-grape-4);
  --panel-info-title-bg: linear-gradient(135deg, var(--oc-grape-4), var(--oc-grape-3));
  --panel-info-title-color: var(--oc-white);
  --panel-info-body-bg: var(--oc-grape-0);
  --panel-info-body-color: var(--oc-grape-8);
  /* Warning Panel */
  --panel-warning-bg: linear-gradient(135deg, var(--oc-orange-0), var(--oc-orange-1));
  --panel-warning-border-color: var(--oc-orange-4);
  --panel-warning-title-bg: linear-gradient(135deg, var(--oc-orange-5), var(--oc-orange-4));
  --panel-warning-title-color: var(--oc-white);
  --panel-warning-body-bg: var(--oc-orange-0);
  --panel-warning-body-color: var(--oc-orange-8);
  /* Danger Panel */
  --panel-danger-bg: linear-gradient(135deg, var(--oc-red-0), var(--oc-red-2));
  --panel-danger-border-color: var(--oc-red-5);
  --panel-danger-box-shadow: 0 2px 8px rgba(245, 101, 101, 0.2);
  --panel-danger-title-bg: linear-gradient(135deg, var(--oc-red-6), var(--oc-red-5));
  --panel-danger-title-color: var(--oc-white);
  --panel-danger-body-bg: var(--oc-red-0);
  --panel-danger-body-color: var(--oc-red-8);
  /* Success Panel */
  --panel-success-bg: linear-gradient(135deg, var(--oc-teal-0), var(--oc-teal-1));
  --panel-success-border-color: var(--oc-teal-4);
  --panel-success-title-bg: linear-gradient(135deg, var(--oc-teal-5), var(--oc-teal-4));
  --panel-success-title-color: var(--oc-white);
  --panel-success-body-bg: var(--oc-teal-0);
  --panel-success-body-color: var(--oc-teal-8);
  --md-toc-bg: var(--oc-white);
  --md-toc-border-color: var(--base-border-color);
  --md-toc-indicator-color: var(--oc-pink-1);
  --md-toc-link-hover-bg: var(--oc-pink-0);
  --md-toc-link-active-bg: var(--oc-pink-1);
}

/** 预览区域样式 */
.cherry-markdown.theme__red h1,
.cherry-markdown.theme__red h2,
.cherry-markdown.theme__red h3,
.cherry-markdown.theme__red h4,
.cherry-markdown.theme__red h5 {
  text-align: center;
  border-bottom: 1px dashed var(--md-hr-border);
  padding-bottom: 15px;
  margin-bottom: 25px;
}

/*
 * 淡雅主题
 */
.cherry.theme__violet {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: var(--oc-violet-9);
  --secondary-color: var(--oc-violet-0);
  --base-font-color: var(--primary-color);
  --base-editor-bg: var(--oc-white);
  --base-previewer-bg: var(--oc-white);
  --base-border-color: var(--oc-violet-8);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-white);
  --toolbar-btn-color: var(--primary-color);
  --toolbar-btn-hover-bg: var(--oc-violet-5);
  --toolbar-btn-hover-color: var(--oc-violet-0);
  --toolbar-btn-active-bg: var(--oc-violet-7);
  --toolbar-split-color: var(--oc-violet-8);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--oc-violet-7);
  --editor-string-color: var(--oc-violet-6);
  --editor-comment-color: var(--oc-violet-6);
  --editor-quote-color: var(--primary-color);
  --editor-link-color: var(--oc-violet-6);
  --editor-url-bg-color: var(--oc-violet-0);
  --editor-v2-color: var(--primary-color);
  --editor-v3-color: var(--oc-violet-7);
  --editor-keyword-color: var(--oc-violet-6);
  --editor-selection-bg: var(--oc-violet-2);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--oc-violet-7);
  --md-paragraph-color: var(--primary-color);
  --md-link-color: var(--oc-violet-6);
  --md-link-hover-color: var(--oc-violet-7);
  --md-inline-code-color: var(--oc-violet-6);
  --md-inline-code-bg: var(--oc-violet-1);
  --md-blockquote-bg: var(--oc-violet-1);
  --md-hr-border: var(--oc-violet-8);
  --md-table-border: var(--oc-violet-8);
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 4px;
  --panel-bg: var(--oc-white);
  --panel-border: 1px solid var(--oc-gray-2);
  --panel-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --panel-title-bg: var(--oc-gray-1);
  --panel-title-color: var(--oc-gray-8);
  --panel-title-border-radius: 2px 2px 0 0;
  --panel-title-border-bottom: 1px solid var(--oc-gray-3);
  --panel-body-bg: var(--oc-white);
  --panel-body-color: var(--oc-gray-8);
  --panel-body-border-radius: 0 0 2px 2px;
  /* Primary Panel */
  --panel-primary-border-color: var(--oc-blue-5);
  --panel-primary-title-bg: var(--oc-blue-1);
  --panel-primary-title-color: var(--oc-blue-8);
  --panel-primary-title-border-bottom: 1px solid var(--oc-blue-3);
  --panel-primary-body-bg: var(--oc-blue-0);
  --panel-primary-body-color: var(--oc-blue-9);
  /* Info Panel */
  --panel-info-border-color: var(--oc-cyan-5);
  --panel-info-title-bg: var(--oc-cyan-1);
  --panel-info-title-color: var(--oc-cyan-8);
  --panel-info-title-border-bottom: 1px solid var(--oc-cyan-3);
  --panel-info-body-bg: var(--oc-cyan-0);
  --panel-info-body-color: var(--oc-cyan-9);
  /* Warning Panel */
  --panel-warning-border-color: var(--oc-yellow-5);
  --panel-warning-title-bg: var(--oc-yellow-1);
  --panel-warning-title-color: var(--oc-yellow-8);
  --panel-warning-title-border-bottom: 1px solid var(--oc-yellow-3);
  --panel-warning-body-bg: var(--oc-yellow-0);
  --panel-warning-body-color: var(--oc-yellow-9);
  /* Danger Panel */
  --panel-danger-border-color: var(--oc-red-5);
  --panel-danger-title-bg: var(--oc-red-1);
  --panel-danger-title-color: var(--oc-red-8);
  --panel-danger-title-border-bottom: 1px solid var(--oc-red-3);
  --panel-danger-body-bg: var(--oc-red-0);
  --panel-danger-body-color: var(--oc-red-9);
  /* Success Panel */
  --panel-success-border-color: var(--oc-green-5);
  --panel-success-title-bg: var(--oc-green-1);
  --panel-success-title-color: var(--oc-green-8);
  --panel-success-title-border-bottom: 1px solid var(--oc-green-3);
  --panel-success-body-bg: var(--oc-green-0);
  --panel-success-body-color: var(--oc-green-9);
  --md-toc-bg: var(--oc-violet-0);
  --md-toc-indicator-color: var(--oc-violet-1);
  --md-toc-link-hover-bg: var(--oc-violet-1);
  --md-toc-link-active-bg: var(--oc-violet-2);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-violet-0);
  --accordion-border: var(--oc-violet-2);
  --accordion-shadow: var(--shadow-sm);
  --accordion-summary-bg: var(--oc-violet-6);
  --accordion-summary-hover-bg: var(--oc-violet-5);
  --accordion-body-bg: var(--oc-violet-0);
  --accordion-body-border: var(--oc-violet-1);
  --accordion-body-color: var(--primary-color);
}

/** 预览区域样式 */
.cherry-markdown.theme__violet h1,
.cherry-markdown.theme__violet h2,
.cherry-markdown.theme__violet h3,
.cherry-markdown.theme__violet h4,
.cherry-markdown.theme__violet h5 {
  text-align: center;
  margin-bottom: 35px;
}

/*
 * 清幽主题
 */
.cherry.theme__blue {
  /* ========== 基础色彩变量覆盖 ========== */
  --primary-color: var(--oc-indigo-6);
  --secondary-color: var(--oc-violet-0);
  --base-font-color: var(--oc-indigo-8);
  --base-editor-bg: var(--oc-violet-0);
  --base-previewer-bg: var(--oc-violet-0);
  --base-border-color: var(--oc-indigo-4);
  /* ========== 工具栏相关变量 ========== */
  --toolbar-bg: var(--oc-violet-1);
  --toolbar-btn-color: var(--oc-indigo-7);
  --toolbar-btn-hover-bg: var(--oc-violet-3);
  --toolbar-btn-hover-color: var(--oc-white);
  --toolbar-btn-active-bg:var(--oc-blue-5);
  --toolbar-split-color: var(--oc-indigo-4);
  --dropdown-bg: var(--oc-white);
  /* ========== 编辑器相关变量 ========== */
  --editor-header-color: var(--primary-color);
  --editor-string-color: var(--oc-indigo-5);
  --editor-comment-color: var(--oc-indigo-5);
  --editor-quote-color: var(--oc-indigo-8);
  --editor-link-color: var(--oc-indigo-5);
  --editor-url-bg-color: var(--oc-violet-1);
  --editor-v2-color: var(--oc-indigo-8);
  --editor-v3-color: var(--primary-color);
  --editor-keyword-color: var(--oc-indigo-5);
  --editor-selection-bg: var(--oc-violet-2);
  /* ========== Markdown 相关变量 ========== */
  --md-heading-color: var(--primary-color);
  --md-paragraph-color: var(--oc-indigo-8);
  --md-link-color: var(--oc-indigo-5);
  --md-link-hover-color: var(--primary-color);
  --md-inline-code-color: var(--oc-indigo-5);
  --md-inline-code-bg: var(--oc-violet-1);
  --md-blockquote-bg: var(--oc-violet-1);
  --md-hr-border: var(--oc-indigo-4);
  --md-table-border: var(--oc-indigo-4);
  /* ========== Panel 相关变量 ========== */
  --panel-border-radius: 0;
  --panel-bg: transparent;
  --panel-border: none;
  --panel-box-shadow: none;
  --panel-title-bg: transparent;
  --panel-title-color: inherit;
  --panel-body-bg: transparent;
  --panel-body-color: inherit;
  /* Primary Panel */
  --panel-primary-border: none;
  --panel-primary-border-color: transparent;
  --panel-primary-title-bg: transparent;
  --panel-primary-bg: transparent;
  --panel-primary-title-color: var(--oc-blue-7);
  --panel-primary-body-color: inherit;
  /* Info Panel */
  --panel-info-border: none;
  --panel-info-border-color: transparent;
  --panel-info-title-bg: transparent;
  --panel-info-bg: transparent;
  --panel-info-title-color: var(--oc-cyan-7);
  --panel-info-body-color: inherit;
  /* Warning Panel */
  --panel-warning-border: none;
  --panel-warning-border-color: transparent;
  --panel-warning-title-bg: transparent;
  --panel-warning-bg: transparent;
  --panel-warning-title-color: var(--oc-yellow-7);
  --panel-warning-body-color: inherit;
  /* Danger Panel */
  --panel-danger-border: none;
  --panel-danger-border-color: transparent;
  --panel-danger-title-bg: transparent;
  --panel-danger-bg: transparent;
  --panel-danger-title-color: var(--oc-pink-7);
  --panel-danger-body-color: inherit;
  /* Success Panel */
  --panel-success-border: none;
  --panel-success-border-color: transparent;
  --panel-success-title-bg: transparent;
  --panel-success-bg: transparent;
  --panel-success-title-color: var(--oc-teal-7);
  --panel-success-body-color: inherit;
  --md-toc-bg: var(--oc-white);
  --md-toc-border-color: var(--base-border-color);
  --md-toc-indicator-color: var(--oc-indigo-1);
  --md-toc-link-hover-bg: var(--oc-violet-0);
  --md-toc-link-active-bg: var(--oc-violet-1);
  /* ========== 手风琴组件变量覆盖 ========== */
  --accordion-bg: var(--oc-violet-0);
  --accordion-border: var(--oc-indigo-2);
  --accordion-shadow: var(--shadow-sm);
  --accordion-summary-bg: var(--oc-indigo-6);
  --accordion-summary-hover-bg: var(--oc-indigo-5);
  --accordion-body-bg: var(--oc-violet-0);
  --accordion-body-border: var(--oc-indigo-1);
  --accordion-body-color: var(--oc-indigo-8);
}

/** 预览区域样式 */
.cherry-markdown.theme__blue {
  /** Blue主题特殊的panel样式 - 使用左边框 */
}
.cherry-markdown.theme__blue h1,
.cherry-markdown.theme__blue h2,
.cherry-markdown.theme__blue h3,
.cherry-markdown.theme__blue h4,
.cherry-markdown.theme__blue h5 {
  text-align: center;
  border-bottom: 1px dashed var(--md-hr-border);
  padding-bottom: 15px;
  margin-bottom: 25px;
}
.cherry-markdown.theme__blue .cherry-panel__primary {
  border-left: 4px solid var(--oc-blue-6);
}
.cherry-markdown.theme__blue .cherry-panel__info {
  border-left: 4px solid var(--oc-cyan-6);
}
.cherry-markdown.theme__blue .cherry-panel__warning {
  border-left: 4px solid var(--oc-yellow-6);
}
.cherry-markdown.theme__blue .cherry-panel__danger {
  border-left: 4px solid var(--oc-pink-6);
}
.cherry-markdown.theme__blue .cherry-panel__success {
  border-left: 4px solid var(--oc-teal-6);
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 98%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 98%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 85%;
    --muted-foreground: 0 0% 40%;
    --accent: 0 0% 90%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 85%;
    --input: 0 0% 85%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 5%;
    --foreground: 0 0% 100%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-auto {
  pointer-events: auto;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-\[50\%\] {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-4 {
  right: 1rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-4 {
  top: 1rem;
}
.top-\[50\%\] {
  top: 50%;
}
.z-10 {
  z-index: 10;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[100\] {
  z-index: 100;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.contents {
  display: contents;
}
.hidden {
  display: none;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[1\.2rem\] {
  height: 1.2rem;
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-\[--radix-select-content-available-height\] {
  max-height: var(--radix-select-content-available-height);
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[70vh\] {
  max-height: 70vh;
}
.max-h-screen {
  max-height: 100vh;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-10 {
  width: 2.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-\[1\.2rem\] {
  width: 1.2rem;
}
.w-\[200px\] {
  width: 200px;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-\[280px\] {
  min-width: 280px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.origin-\[--radix-popover-content-transform-origin\] {
  transform-origin: var(--radix-popover-content-transform-origin);
}
.origin-\[--radix-select-content-transform-origin\] {
  transform-origin: var(--radix-select-content-transform-origin);
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-text {
  cursor: text;
}
.touch-none {
  touch-action: none;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-4 {
  gap: 1rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-none {
  border-style: none;
}
.border-border {
  border-color: hsl(var(--border));
}
.border-destructive {
  border-color: hsl(var(--destructive));
}
.border-input {
  border-color: hsl(var(--input));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-secondary {
  border-color: hsl(var(--secondary));
}
.border-transparent {
  border-color: transparent;
}
.border-l-primary {
  border-left-color: hsl(var(--primary));
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-accent {
  background-color: hsl(var(--accent));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-background\/95 {
  background-color: hsl(var(--background) / 0.95);
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-card\/30 {
  background-color: hsl(var(--card) / 0.3);
}
.bg-card\/50 {
  background-color: hsl(var(--card) / 0.5);
}
.bg-card\/95 {
  background-color: hsl(var(--card) / 0.95);
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-muted\/30 {
  background-color: hsl(var(--muted) / 0.3);
}
.bg-muted\/40 {
  background-color: hsl(var(--muted) / 0.4);
}
.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}
.bg-muted\/60 {
  background-color: hsl(var(--muted) / 0.6);
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-transparent {
  background-color: transparent;
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-primary {
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-primary\/70 {
  --tw-gradient-to: hsl(var(--primary) / 0.7) var(--tw-gradient-to-position);
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.fill-current {
  fill: currentColor;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[1px\] {
  padding: 1px;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-4 {
  padding-top: 1rem;
}
.text-center {
  text-align: center;
}
.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.italic {
  font-style: italic;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-foreground\/50 {
  color: hsl(var(--foreground) / 0.5);
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-muted-foreground\/60 {
  color: hsl(var(--muted-foreground) / 0.6);
}
.text-muted-foreground\/80 {
  color: hsl(var(--muted-foreground) / 0.8);
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
  opacity: 0;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter {
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
/* Custom scrollbar for sidebar */
.sidebar-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
.sidebar-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
.sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }
.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }
/* Line clamp utilities */
.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
/* Improved backdrop blur */
/* Sidebar note card hover effects */
/* Resizable handle custom styles */
.resizable-handle-custom {
    position: relative;
    background: hsl(var(--border));
    transition: background-color 0.2s ease;
  }
.resizable-handle-custom::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 20px;
    background: hsl(var(--muted-foreground) / 0.3);
  }
.resizable-handle-custom:hover {
    background: hsl(var(--accent));
  }
.resizable-handle-custom:hover::before {
    background: hsl(var(--muted-foreground) / 0.6);
  }
/* Gradient text effect */

/* Import Cherry Markdown CSS */

/* Cherry Markdown Editor Custom Styles */
.cherry-markdown-editor {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.cherry-markdown-editor .cherry {
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 6px !important;
  height: 100% !important;
  min-height: 400px !important;
  display: flex !important;
  flex-direction: column !important;
}

.cherry-markdown-editor .cherry-toolbar {
  flex-shrink: 0 !important;
}

.cherry-markdown-editor .cherry-editor,
.cherry-markdown-editor .cherry-previewer {
  flex: 1 !important;
  min-height: 0 !important;
  overflow-y: auto !important;
  font-size: 14px !important;
}

.cherry-markdown-editor .cherry-previewer {
  padding: 16px !important;
}

.cherry-markdown-editor .cherry-previewer h1 {
  font-size: 1.5em !important;
  margin-top: 0 !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h2 {
  font-size: 1.3em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h3 {
  font-size: 1.2em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h4 {
  font-size: 1.1em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h5 {
  font-size: 1em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h6 {
  font-size: 0.9em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer p {
  font-size: 14px !important;
  line-height: 1.6 !important;
  margin-bottom: 1em !important;
}

.cherry-markdown-editor .cherry-previewer ul,
.cherry-markdown-editor .cherry-previewer ol {
  margin-bottom: 1em !important;
  padding-left: 1.5em !important;
}

.cherry-markdown-editor .cherry-previewer li {
  margin-bottom: 0.25em !important;
}

.cherry-markdown-editor .cherry-previewer blockquote {
  border-left: 4px solid hsl(var(--border)) !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  color: hsl(var(--muted-foreground)) !important;
}

.cherry-markdown-editor .cherry-previewer code {
  background-color: hsl(var(--muted)) !important;
  padding: 0.2em 0.4em !important;
  border-radius: 3px !important;
  font-size: 0.9em !important;
}

.cherry-markdown-editor .cherry-previewer pre {
  background-color: hsl(var(--muted)) !important;
  padding: 1em !important;
  border-radius: 6px !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.cherry-markdown-editor .cherry-previewer pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Fix for CodeMirror editor */
.cherry-markdown-editor .CodeMirror {
  height: 100% !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.cherry-markdown-editor .CodeMirror-scroll {
  min-height: 100% !important;
}

/* Dark mode adjustments */
.dark .cherry-markdown-editor .cherry-previewer {
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

.dark .cherry-markdown-editor .cherry-previewer h1,
.dark .cherry-markdown-editor .cherry-previewer h2,
.dark .cherry-markdown-editor .cherry-previewer h3,
.dark .cherry-markdown-editor .cherry-previewer h4,
.dark .cherry-markdown-editor .cherry-previewer h5,
.dark .cherry-markdown-editor .cherry-previewer h6 {
  color: hsl(var(--foreground)) !important;
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground\/60::-moz-placeholder {
  color: hsl(var(--muted-foreground) / 0.6);
}

.placeholder\:text-muted-foreground\/60::placeholder {
  color: hsl(var(--muted-foreground) / 0.6);
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-y-0::after {
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}

.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}

.after\:w-1::after {
  content: var(--tw-content);
  width: 0.25rem;
}

.after\:-translate-x-1\/2::after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-ring:focus-within {
  --tw-ring-color: hsl(var(--ring));
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:border-l-accent\/50:hover {
  border-left-color: hsl(var(--accent) / 0.5);
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-accent\/40:hover {
  background-color: hsl(var(--accent) / 0.4);
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-secondary:hover {
  background-color: hsl(var(--secondary));
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-muted\/50:focus {
  background-color: hsl(var(--muted) / 0.5);
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:opacity-100:focus {
  opacity: 1;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
  border-color: hsl(var(--muted) / 0.4);
}

.group.destructive .group-\[\.destructive\]\:text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
  border-color: hsl(var(--destructive) / 0.3);
}

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
  background-color: hsl(var(--destructive));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
  color: hsl(var(--destructive-foreground));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
  height: 1px;
}

.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
  width: 100%;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
  flex-direction: column;
}

.data-\[selected\=\'true\'\]\:bg-accent[data-selected='true'] {
  background-color: hsl(var(--accent));
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: hsl(var(--accent-foreground));
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: 0.5;
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
  transition-property: none;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
  --tw-exit-opacity: 0.8;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  left: 0px;
}

.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  height: 0.25rem;
}

.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  width: 100%;
}

.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]::after {
  content: var(--tw-content);
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@supports (backdrop-filter: var(--tw)) {

  .supports-\[backdrop-filter\]\:bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }

  .supports-\[backdrop-filter\]\:bg-card\/60 {
    background-color: hsl(var(--card) / 0.6);
  }
}

.dark\:-rotate-90:is(.dark *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:rotate-0:is(.dark *) {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-0:is(.dark *) {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-100:is(.dark *) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 640px) {

  .sm\:bottom-0 {
    bottom: 0px;
  }

  .sm\:right-0 {
    right: 0px;
  }

  .sm\:top-auto {
    top: auto;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg {
    border-radius: var(--radius);
  }

  .sm\:text-left {
    text-align: left;
  }

  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }
}

@media (min-width: 768px) {

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:max-w-\[420px\] {
    max-width: 420px;
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction=vertical]>div {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: hsl(var(--muted-foreground));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {
  padding-top: 0px;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

