@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Cherry Markdown CSS */
@import 'cherry-markdown/dist/cherry-markdown.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 98%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 98%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 85%;
    --muted-foreground: 0 0% 40%;
    --accent: 0 0% 90%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 85%;
    --input: 0 0% 85%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 5%;
    --foreground: 0 0% 100%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
  }
}

@layer base {
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer utilities {
  /* Custom scrollbar for sidebar */
  .sidebar-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Improved backdrop blur */
  .backdrop-blur-custom {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Sidebar note card hover effects */
  .note-card-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .note-card-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px -2px hsl(var(--foreground) / 0.1);
  }

  /* Resizable handle custom styles */
  .resizable-handle-custom {
    position: relative;
    background: hsl(var(--border));
    transition: background-color 0.2s ease;
  }
  
  .resizable-handle-custom::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 20px;
    background: hsl(var(--muted-foreground) / 0.3);
  }
  
  .resizable-handle-custom:hover {
    background: hsl(var(--accent));
  }
  
  .resizable-handle-custom:hover::before {
    background: hsl(var(--muted-foreground) / 0.6);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Cherry Markdown Editor Custom Styles */
.cherry-markdown-editor {
  height: 100% !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  overflow: hidden !important;
  contain: layout style !important;
}

.cherry-markdown-editor .cherry {
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 6px !important;
  height: 100% !important;
  width: 100% !important;
  min-height: 400px !important;
  max-height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

.cherry-markdown-editor .cherry-toolbar {
  flex-shrink: 0 !important;
  position: relative !important;
  z-index: 1 !important;
}

.cherry-markdown-editor .cherry-editor,
.cherry-markdown-editor .cherry-previewer {
  flex: 1 !important;
  min-height: 0 !important;
  max-height: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  font-size: 14px !important;
  position: relative !important;
  box-sizing: border-box !important;
}

.cherry-markdown-editor .cherry-previewer {
  padding: 16px !important;
}

.cherry-markdown-editor .cherry-previewer h1 {
  font-size: 1.5em !important;
  margin-top: 0 !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h2 {
  font-size: 1.3em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h3 {
  font-size: 1.2em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h4 {
  font-size: 1.1em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h5 {
  font-size: 1em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer h6 {
  font-size: 0.9em !important;
  margin-top: 1em !important;
  margin-bottom: 0.5em !important;
}

.cherry-markdown-editor .cherry-previewer p {
  font-size: 14px !important;
  line-height: 1.6 !important;
  margin-bottom: 1em !important;
}

.cherry-markdown-editor .cherry-previewer ul,
.cherry-markdown-editor .cherry-previewer ol {
  margin-bottom: 1em !important;
  padding-left: 1.5em !important;
}

.cherry-markdown-editor .cherry-previewer li {
  margin-bottom: 0.25em !important;
}

.cherry-markdown-editor .cherry-previewer blockquote {
  border-left: 4px solid hsl(var(--border)) !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  color: hsl(var(--muted-foreground)) !important;
}

.cherry-markdown-editor .cherry-previewer code {
  background-color: hsl(var(--muted)) !important;
  padding: 0.2em 0.4em !important;
  border-radius: 3px !important;
  font-size: 0.9em !important;
}

.cherry-markdown-editor .cherry-previewer pre {
  background-color: hsl(var(--muted)) !important;
  padding: 1em !important;
  border-radius: 6px !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.cherry-markdown-editor .cherry-previewer pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Fix for CodeMirror editor */
.cherry-markdown-editor .CodeMirror {
  height: 100% !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.cherry-markdown-editor .CodeMirror-scroll {
  min-height: 100% !important;
}

/* Dark mode adjustments */
.dark .cherry-markdown-editor .cherry-previewer {
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

.dark .cherry-markdown-editor .cherry-previewer h1,
.dark .cherry-markdown-editor .cherry-previewer h2,
.dark .cherry-markdown-editor .cherry-previewer h3,
.dark .cherry-markdown-editor .cherry-previewer h4,
.dark .cherry-markdown-editor .cherry-previewer h5,
.dark .cherry-markdown-editor .cherry-previewer h6 {
  color: hsl(var(--foreground)) !important;
}

/* Resizable Panel Layout Fixes */
[data-panel-group-direction="horizontal"] {
  height: 100% !important;
}

[data-panel-group-direction="horizontal"] > [data-panel] {
  height: 100% !important;
  overflow: hidden !important;
}

/* Ensure Cherry editor stays within its container */
.cherry-markdown-editor .cherry {
  position: relative !important;
  transform: none !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
}

/* Fix for any absolute positioning issues */
.cherry-markdown-editor .cherry * {
  box-sizing: border-box !important;
}

.cherry-markdown-editor .cherry .cherry-editor,
.cherry-markdown-editor .cherry .cherry-previewer {
  position: relative !important;
  transform: none !important;
}

/* Additional containment fixes */
.cherry-markdown-editor .cherry .cherry-editor {
  width: 50% !important;
}

.cherry-markdown-editor .cherry .cherry-previewer {
  width: 50% !important;
}

/* When in preview-only mode */
.cherry-markdown-editor .cherry.cherry--previewOnly .cherry-previewer {
  width: 100% !important;
}

/* When in edit-only mode */
.cherry-markdown-editor .cherry.cherry--editOnly .cherry-editor {
  width: 100% !important;
}

/* Ensure proper flex behavior */
.cherry-markdown-editor .cherry {
  flex-wrap: nowrap !important;
}

.cherry-markdown-editor .cherry > * {
  flex-shrink: 0 !important;
}
