"use client";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { NoteList } from "@/components/NoteList";
import { NoteEditor } from "@/components/NoteEditor";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { ThemeToggle } from "@/components/theme-toggle";
import { Menu, PanelLeftClose, PanelLeftOpen } from "lucide-react";
import Link from "next/link";
import { Note, NoteSummary } from "@/types";
import { useI18n } from "@/contexts/i18n";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";

function EditPageContent() {
  const { t } = useI18n();
  const [notes, setNotes] = useState<NoteSummary[]>([]);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isNewNote, setIsNewNote] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { toast } = useToast();
  const searchParams = useSearchParams();

  const loadNotes = useCallback(async () => {
    if (typeof window.electron?.getNotes !== "function") {
      setLoading(false);
      toast({
        title: t("edit.error"),
        description: t("edit.electron_api_unavailable"),
        variant: "destructive",
      });
      return;
    }
    try {
      const allNotes = await window.electron.getNotes();
      const noteSummaries = allNotes.map((note) => ({
        id: note.id,
        title: note.title,
        content: note.content,
        tags: note.tags,
      }));
      setNotes(noteSummaries);

      const noteId = searchParams.get("id");
      if (noteId) {
        handleSelectNote(noteId);
      } else {
        setIsNewNote(true);
      }
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : t("edit.cannot_load_notes");
      toast({
        title: t("edit.load_failed"),
        description: message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast, searchParams]);

  useEffect(() => {
    loadNotes();
  }, [loadNotes]);

  const handleSelectNote = async (id: string) => {
    try {
      const note = await window.electron.getNote(id);
      setSelectedNote(note);
      setIsNewNote(false);
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : t("edit.cannot_load_note_content");
      toast({
        title: t("edit.load_failed"),
        description: message,
        variant: "destructive",
      });
    }
  };

  const handleSaveNote = async (
    id: string | null,
    title: string,
    content: string,
    tags: string[]
  ) => {
    try {
      let savedNote: Note;
      if (id) {
        savedNote = await window.electron.updateNote(id, title, content, tags);
      } else {
        savedNote = await window.electron.createNote(title, content, tags);
      }
      setSelectedNote(savedNote);
      setIsNewNote(false);
      
      // 更新筆記列表狀態，而不是重新載入
      setNotes(prevNotes => {
        const noteIndex = prevNotes.findIndex(n => n.id === savedNote.id);
        if (noteIndex > -1) {
          // 更新現有筆記
          const newNotes = [...prevNotes];
          newNotes[noteIndex] = {
            id: savedNote.id,
            title: savedNote.title,
            content: savedNote.content,
            tags: savedNote.tags,
          };
          return newNotes;
        } else {
          // 新增筆記
          return [
            {
              id: savedNote.id,
              title: savedNote.title,
              content: savedNote.content,
              tags: savedNote.tags,
            },
            ...prevNotes,
          ];
        }
      });

      toast({
        title: t("edit.save_success"),
        description: t("edit.note_saved"),
      });
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t("edit.cannot_save_note");
      toast({
        title: t("edit.save_failed"),
        description: message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteNote = async (id: string) => {
    try {
      await window.electron.deleteNote(id);
      setSelectedNote(null);
      await loadNotes();
      toast({
        title: t("edit.delete_success"),
        description: t("edit.note_deleted"),
      });
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : t("edit.cannot_delete_note");
      toast({
        title: t("edit.delete_failed"),
        description: message,
        variant: "destructive",
      });
    }
  };

  const handleNewNote = () => {
    setSelectedNote(null);
    setIsNewNote(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">{t("edit.loading")}</div>
    );
  }

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Mobile Sidebar Overlay */}
      <div className="md:hidden">
        <div
          className={`
            ${sidebarOpen ? "translate-x-0" : "-translate-x-full"}
            fixed inset-y-0 left-0 z-50 w-80 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 border-r shadow-lg transition-transform duration-300 ease-in-out
          `}
        >
          <div className="flex flex-col h-full p-4">
            <div className="flex items-center justify-between mb-4">
              <Link href="/" className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <h1 className="text-2xl font-bold">MyNote</h1>
              </Link>
              <div className="flex items-center gap-2">
                <ThemeToggle />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(false)}
                >
                  <PanelLeftClose className="w-5 h-5" />
                </Button>
              </div>
            </div>
            <Button variant="outline" onClick={handleNewNote} className="mb-4">
              {t("edit.new_note")}
            </Button>
            <div className="flex-1 overflow-hidden">
              <NoteList
                notes={notes}
                onSelectNote={(id) => {
                  handleSelectNote(id);
                  setSidebarOpen(false);
                }}
                selectedNoteId={selectedNote?.id}
              />
            </div>
          </div>
        </div>
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </div>

      {/* Desktop Layout with Resizable Panels */}
      <div className="hidden w-full h-full md:flex">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Sidebar Panel */}
          <ResizablePanel
            defaultSize={25}
            minSize={20}
            maxSize={40}
            className="min-w-[280px]"
          >
            <div className="flex flex-col h-full bg-card/30">
              <div className="flex items-center justify-between p-4 bg-card/50">
                <Link href="/" className="flex items-center gap-2 transition-opacity hover:opacity-80">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6 text-primary"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                  <h1 className="text-xl font-bold text-transparent bg-gradient-to-r from-primary to-primary/70 bg-clip-text">MyNote</h1>
                </Link>
                <ThemeToggle />
              </div>
              
              <div className="p-4">
                <Button
                  variant="default"
                  onClick={handleNewNote}
                  className="w-full shadow-sm bg-primary hover:bg-primary/90 text-primary-foreground"
                  size="sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline><path d="M12 18v-6"></path><path d="M9 15h6"></path></svg>
                  {t("edit.new_note")}
                </Button>
              </div>
              
              <div className="flex-1 p-2 overflow-hidden">
                <NoteList
                  notes={notes}
                  onSelectNote={handleSelectNote}
                  selectedNoteId={selectedNote?.id}
                />
              </div>
            </div>
          </ResizablePanel>

          {/* Resize Handle */}
          <ResizableHandle withHandle className="resizable-handle-custom" />

          {/* Main Content Panel */}
          <ResizablePanel defaultSize={75} minSize={50} className="relative">
            <main className="absolute inset-0 flex flex-col overflow-hidden">
              {selectedNote || isNewNote ? (
                <div className="flex flex-col h-full p-6 overflow-hidden">
                  <NoteEditor
                    note={selectedNote}
                    onSave={handleSaveNote}
                    onDelete={handleDeleteNote}
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="space-y-4 text-center">
                    <div className="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
                    </div>
                    <h2 className="text-2xl font-semibold">{t("edit.welcome_title")}</h2>
                    <p className="max-w-md text-muted-foreground">
                      {t("edit.welcome_description")}
                    </p>
                  </div>
                </div>
              )}
            </main>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="flex items-center justify-between p-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <PanelLeftOpen className="w-5 h-5" />
          </Button>
          <h1 className="text-lg font-semibold">MyNote</h1>
          <ThemeToggle />
        </div>
      </div>

      {/* Mobile Main Content */}
      <div className="w-full h-full pt-16 md:hidden">
        {selectedNote || isNewNote ? (
          <div className="h-full p-4 flex flex-col">
            <NoteEditor
              note={selectedNote}
              onSave={handleSaveNote}
              onDelete={handleDeleteNote}
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full p-8">
            <div className="space-y-4 text-center">
              <div className="flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10">
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
              </div>
              <h2 className="text-2xl font-semibold">{t("edit.welcome_title")}</h2>
              <p className="max-w-md text-muted-foreground">
                {t("edit.welcome_description")}
              </p>
            </div>
          </div>
        )}
      </div>
      
      <Toaster />
    </div>
  );
}

export default function EditPage() {
  return (
    <Suspense fallback={<div>載入中...</div>}>
      <EditPageContent />
    </Suspense>
  );
}