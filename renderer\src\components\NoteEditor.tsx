 "use client";

import { useState, useEffect, useRef } from "react";
import { useTheme } from "next-themes";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TagInput } from "@/components/ui/TagInput";
import CherryMarkdownEditor, { CherryMarkdownEditorRef } from "./CherryMarkdownEditor";
import MarkdownPreview from "@uiw/react-markdown-preview";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import {
  summarize,
  generateTitle,
  polishWithOpenRouter,
  expandContent,
  expandContentStream,
  summarizeStream,
  generateTitleStream,
  polishStream,
  changeToneStream,
  changeToneWithOpenRouter,
  generateTags,
} from "@/lib/ai-service";
import { Save, Trash2, Sparkles, Loader2, Edit } from "lucide-react";
import { useI18n } from "@/contexts/i18n";

interface Note {
  id: string;
  title: string;
  content: string;
  tags: string[];
}

interface NoteEditorProps {
  note: Note | null;
  onSave: (
    id: string | null,
    title: string,
    content: string,
    tags: string[]
  ) => void;
  onDelete: (id: string) => void;
}

type AiLoadingState =
  | false
  | "summarize"
  | "generateTitle"
  | "polish"
  | "expand"
  | "changeTone"
  | "generateTags";

export function NoteEditor({ note, onSave, onDelete }: NoteEditorProps) {
  const { t } = useI18n();
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const { theme } = useTheme();
  const { toast } = useToast();

  const [aiLoading, setAiLoading] = useState<AiLoadingState>(false);
  const [selectedText, setSelectedText] = useState("");
  const [aiResult, setAiResult] = useState("");
  const [isResultDialogVisible, setIsResultDialogVisible] = useState(false);
  const [isExpandDialogVisible, setIsExpandDialogVisible] = useState(false);
  const [expandPrompt, setExpandPrompt] = useState("");
  const [suggestedTitle, setSuggestedTitle] = useState("");
  const [isTitleAlertVisible, setIsTitleAlertVisible] = useState(false);

  const editorRef = useRef<CherryMarkdownEditorRef>(null);

  // 選取文字處理將由 CherryMarkdownEditor 元件處理
  const handleSelectionChange = (selectedText: string) => {
    setSelectedText(selectedText);
  };

  useEffect(() => {
    if (note) {
      setTitle(note.title);
      setContent(note.content);
      setTags(Array.isArray(note.tags) ? note.tags : []);
      setIsEditing(false);
    } else {
      setTitle("");
      setContent("");
      setTags([]);
      setIsEditing(true);
    }
  }, [note]);

  const handleSave = () => {
    onSave(note?.id || null, title, content, tags);
    setIsEditing(false);
  };

  const handleDelete = () => {
    if (note) {
      onDelete(note.id);
    }
  };

  const handleAiAction = async (
    action: AiLoadingState,
    payload?: any
  ): Promise<void> => {
    setAiLoading(action);
    try {
      let result: string;
      switch (action) {
        case "summarize":
          setAiResult(""); // 清空之前的結果
          setIsResultDialogVisible(true);

          // 立即打開 result dialog 並開始 streaming
          console.log("Starting summarize streaming");
          summarizeStream(content, (chunk) => {
            console.log("Received summarize chunk:", chunk);
            setAiResult((prev) => prev + chunk);
          }).catch((error) => {
            console.error("Summarize streaming error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
          });
          break;
        case "generateTitle":
          let titleResult = "";
          console.log("Starting title generation streaming");
          
          await generateTitleStream(content, (chunk) => {
            console.log("Received title chunk:", chunk);
            titleResult += chunk;
          }).catch((error) => {
            console.error("Title streaming error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
            throw error;
          });
          
          setSuggestedTitle(titleResult.trim());
          setIsTitleAlertVisible(true);
          break;
        case "polish":
          if (!selectedText) {
            toast({
              title: t("editor.selection_error"),
              description: t("editor.select_text_first"),
              variant: "destructive",
            });
            return;
          }
          setAiResult(""); // 清空之前的結果
          setIsResultDialogVisible(true);

          // 立即打開 result dialog 並開始 streaming
          console.log("Starting polish streaming");
          polishStream(selectedText, (chunk) => {
            console.log("Received polish chunk:", chunk);
            setAiResult((prev) => prev + chunk);
          }).catch((error) => {
            console.error("Polish streaming error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
          });
          break;
        case "expand":
          if (!payload || !payload.trim()) {
            toast({
              title: t("editor.input_error"),
              description: t("editor.enter_valid_prompt"),
              variant: "destructive",
            });
            return;
          }
          setAiResult(""); // 清空之前的結果
          setIsExpandDialogVisible(false);
          setIsResultDialogVisible(true);

          // 立即打開 result dialog 並開始 streaming
          console.log("Starting expand streaming with payload:", payload);
          expandContentStream(payload, (chunk) => {
            console.log("Received expand chunk:", chunk);
            setAiResult((prev) => prev + chunk);
          }).catch((error) => {
            console.error("Expand streaming error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
          });
          break;
        case "changeTone":
          if (!selectedText) {
            toast({
              title: t("editor.selection_error"),
              description: t("editor.select_tone_text_first"),
              variant: "destructive",
            });
            return;
          }
          setAiResult(""); // 清空之前的結果
          setIsResultDialogVisible(true);

          // 立即打開 result dialog 並開始 streaming
          console.log("Starting changeTone streaming with tone:", payload);
          changeToneStream(selectedText, payload, (chunk) => {
            console.log("Received changeTone chunk:", chunk);
            setAiResult((prev) => prev + chunk);
          }).catch((error) => {
            console.error("ChangeTone streaming error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
          });
          break;
        case "generateTags":
          if (!content.trim()) {
            toast({
              title: t("editor.input_error"),
              description: t("editor.content_required_for_tags"),
              variant: "destructive",
            });
            return;
          }
          try {
            console.log("Starting tag generation");
            const newTags = await generateTags(content);
            const uniqueTags = [...new Set([...tags, ...newTags])];
            setTags(uniqueTags);
            toast({
              title: t("editor.tags_generated"),
              description: t("editor.tags_added_successfully"),
              variant: "default",
            });
          } catch (error) {
            console.error("Tag generation error:", error);
            toast({
              title: t("editor.ai_operation_failed"),
              description: error instanceof Error ? error.message : t("editor.unknown_error"),
              variant: "destructive",
            });
          }
          break;
      }
    } catch (error: unknown) {
      const message =
        error instanceof Error ? error.message : t("editor.unknown_error");
      toast({
        title: t("editor.ai_operation_failed"),
        description: message,
        variant: "destructive",
      });
    } finally {
      setAiLoading(false);
    }
  };

  const handleInsertResult = () => {
    setContent(`${content}\n\n---\n\n${aiResult}`);
    setIsResultDialogVisible(false);
  };

  const handleReplaceResult = () => {
    setContent(content.replace(selectedText, aiResult));
    setIsResultDialogVisible(false);
  };

  return (
    <>
      <div className="flex flex-col h-full">
        <div className="flex-shrink-0 pb-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {isEditing ? (
                <Input
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder={t("editor.title_placeholder")}
                  className="h-auto max-w-2xl px-0 py-1 text-xl font-semibold bg-transparent border-none shadow-none focus-visible:ring-0"
                />
              ) : (
                <h2 className="text-xl font-semibold">{title}</h2>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={!!aiLoading}>
                    {aiLoading ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="w-4 h-4 mr-2" />
                    )}
                    {t("editor.ai_tools")}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleAiAction("summarize")}>
                    {t("editor.summarize")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleAiAction("generateTitle")}
                  >
                    {t("editor.generate_title")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleAiAction("polish")}
                    disabled={!selectedText}
                  >
                    {t("editor.polish_selected")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setIsExpandDialogVisible(true)}
                  >
                    {t("editor.expand_content")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleAiAction("changeTone", "專業")}
                    disabled={!selectedText}
                  >
                    {t("editor.change_tone_professional")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleAiAction("changeTone", "休閒")}
                    disabled={!selectedText}
                  >
                    {t("editor.change_tone_casual")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleAiAction("generateTags")}
                    disabled={!content.trim() || !!aiLoading}
                  >
                    {t("editor.generate_tags")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {isEditing ? (
                <Button onClick={handleSave} size="sm" variant="secondary">
                  <Save className="w-4 h-4 mr-2" />
                  {t("editor.save")}
                </Button>
              ) : (
                <Button
                  onClick={() => setIsEditing(true)}
                  size="sm"
                  variant="outline"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  {t("editor.edit")}
                </Button>
              )}
              {note && (
                <Button variant="destructive" onClick={handleDelete} size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  {t("editor.delete")}
                </Button>
              )}
            </div>
          </div>
          <div className="mt-2">
            {isEditing ? (
              <TagInput
                value={tags}
                onChange={setTags}
                placeholder={t("editor.tags_placeholder")}
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {Array.isArray(tags) &&
                  tags.map((tag) => (
                    <div
                      key={tag}
                      className="px-3 py-1 text-sm rounded-full bg-secondary text-secondary-foreground"
                    >
                      {tag}
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-col flex-1 min-h-0 pt-4">
          <CherryMarkdownEditor
            ref={editorRef}
            value={content}
            onChange={(value) => setContent(value || "")}
            preview={isEditing ? "edit" : "preview"}
            hideToolbar={!isEditing}
            className="h-full min-h-[400px]"
            onSelectionChange={handleSelectionChange}
          />
        </div>
      </div>

      {/* AI Result Dialog */}
      <Dialog
        open={isResultDialogVisible}
        onOpenChange={setIsResultDialogVisible}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("editor.ai_result_title")}</DialogTitle>
          </DialogHeader>
          <div className="p-4 my-4 border rounded-md bg-muted max-h-[70vh] overflow-auto">
            <MarkdownPreview source={aiResult} />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsResultDialogVisible(false)}
            >
              {t("editor.cancel")}
            </Button>
            <Button onClick={handleInsertResult} variant="secondary">{t("editor.insert_at_end")}</Button>
            {selectedText && (
              <Button onClick={handleReplaceResult} variant="secondary">{t("editor.replace_selected")}</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Expand Content Dialog */}
      <Dialog
        open={isExpandDialogVisible}
        onOpenChange={setIsExpandDialogVisible}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("editor.expand_content_title")}</DialogTitle>
          </DialogHeader>
          <Textarea
            placeholder={t("editor.expand_placeholder")}
            value={expandPrompt}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setExpandPrompt(e.target.value)}
            disabled={aiLoading === "expand"}
          />
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsExpandDialogVisible(false)}
              disabled={aiLoading === "expand"}
            >
              {t("editor.cancel")}
            </Button>
            <Button
              onClick={() => handleAiAction("expand", expandPrompt)}
              variant="secondary"
              disabled={!expandPrompt.trim() || aiLoading === "expand"}
            >
              {aiLoading === "expand" ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t("editor.generating")}
                </>
              ) : (
                t("editor.generate")
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Generate Title Alert */}
      <AlertDialog
        open={isTitleAlertVisible}
        onOpenChange={setIsTitleAlertVisible}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("editor.suggested_title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("editor.suggested_title_desc")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="p-4 font-semibold border rounded-md bg-muted">
            {suggestedTitle}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("editor.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setTitle(suggestedTitle);
                setIsTitleAlertVisible(false);
              }}
              className="text-white bg-black hover:bg-gray-800"
            >
              {t("editor.replace")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}